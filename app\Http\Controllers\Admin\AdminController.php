<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\User;
use App\Models\Region;
use Auth;
use Alert;
use App\Models\AppUser;
use App\Models\KurirShipmentRequest;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Milon\Barcode\DNS1D;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of the resource.
     *
     * @return void
     */
    public function index(Request $request)
    {
        $user = AppUser::join('warehouse_location', 'app_user.warehouse_id', 'warehouse_location.warehouse_id')
                        ->where('app_user.user_id', Auth::id())
                        ->select('app_user.*', 'warehouse_location.warehouse_type')->first();
        
        if($user->warehouse_type == 'DUPLICATE') {
            $query = KurirShipmentRequest::where('item_description', 'LIKE', '%by.u store_%')->where('isprint', 'N');
        } else {
            $query = KurirShipmentRequest::where('warehouse_id', $user->warehouse_id)->where('item_description', 'LIKE', '%by.u store_%')->where('isprint', 'N');
        }

        if ($request->filled('daterange')) {
            $dates = explode(' - ', $request->daterange);
            $startDate = Carbon::createFromFormat('M d, Y', $dates[0])->startOfDay();
            $endDate = Carbon::createFromFormat('M d, Y', $dates[1])->endOfDay();
            $query->whereBetween('request_datetime', [$startDate, $endDate]);
        }

        $shipmentRequest = $query->latest('request_datetime')->get();

        $data['shipmentRequest'] = $shipmentRequest;
        return view('admin.dashboard', $data);
    }

    public function indexHistory(Request $request)
    {
        $user = AppUser::join('warehouse_location', 'app_user.warehouse_id', 'warehouse_location.warehouse_id')
                        ->where('app_user.user_id', Auth::id())
                        ->select('app_user.*', 'warehouse_location.warehouse_type')->first();
        
        if($user->warehouse_type == 'DUPLICATE') {
            $query = KurirShipmentRequest::where('item_description', 'LIKE', '%by.u store_%')->where('isprint', 'Y');
        } else {
            $query = KurirShipmentRequest::where('warehouse_id', $user->warehouse_id)->where('item_description', 'LIKE', '%by.u store_%')->where('isprint', 'Y');
        }

        if ($request->filled('daterange')) {
            $dates = explode(' - ', $request->daterange);
            $startDate = Carbon::createFromFormat('M d, Y', $dates[0])->startOfDay();
            $endDate = Carbon::createFromFormat('M d, Y', $dates[1])->endOfDay();
            $query->whereBetween('request_datetime', [$startDate, $endDate]);
        }

        $shipmentRequest = $query->latest('request_datetime')->get();

        $data['shipmentRequest'] = $shipmentRequest;
        return view('admin.shipment-request.history', $data);
    }

    public function showResi($id) {
        $shipmentRequest = KurirShipmentRequest::find($id);
        $shipmentRequest->isprint = 'Y';
        $shipmentRequest->save();
        $shipmentRequest->item_description = substr(strstr($shipmentRequest->item_description, 'by.u store_'), strlen('by.u store_'));
        $shipmentRequest->request_datetime = Carbon::parse($shipmentRequest->request_datetime)->isoFormat('D MMM, YYYY H:mm');
        $barcode = new DNS1D();
        $barcodeImage = $barcode->getBarcodePNG($shipmentRequest->awb, 'C39');
        $data['barcodeImageData'] = base64_encode($barcodeImage);
        $data['shipmentRequest'] = $shipmentRequest;
        return view('admin.template-resi.resi', $data);
    }

}
