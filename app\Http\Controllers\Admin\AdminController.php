<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\User;
use App\Models\Region;
use Auth;
use Alert;
use App\Models\AppUser;
use App\Models\KurirShipmentRequest;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Milon\Barcode\DNS1D;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of the resource.
     *
     * @return void
     */
    public function index(Request $request)
    {
        $user = AppUser::join('warehouse_location', 'app_user.warehouse_id', 'warehouse_location.warehouse_id')
                        ->where('app_user.user_id', Auth::id())
                        ->select('app_user.*', 'warehouse_location.warehouse_type')->first();

        // Base query setup
        if($user->warehouse_type == 'DUPLICATE') {
            $query = KurirShipmentRequest::where('item_description', 'LIKE', '%by.u store_%');
        } else {
            $query = KurirShipmentRequest::where('warehouse_id', $user->warehouse_id)->where('item_description', 'LIKE', '%by.u store_%');
        }

        // Apply filters
        if ($request->filled('daterange')) {
            $dates = explode(' - ', $request->daterange);
            $startDate = Carbon::createFromFormat('M d, Y', $dates[0])->startOfDay();
            $endDate = Carbon::createFromFormat('M d, Y', $dates[1])->endOfDay();
            $query->whereBetween('request_datetime', [$startDate, $endDate]);
        }

        if ($request->filled('courier_service')) {
            $query->where('courier_service', $request->courier_service);
        }

        if ($request->filled('status')) {
            $query->where('isprint', $request->status);
        } else {
            // Default to show pending requests if no status filter
            $query->where('isprint', 'N');
        }

        if ($request->filled('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('awb', 'LIKE', "%{$search}%")
                  ->orWhere('shipper_name', 'LIKE', "%{$search}%")
                  ->orWhere('receiver_name', 'LIKE', "%{$search}%")
                  ->orWhere('item_description', 'LIKE', "%{$search}%");
            });
        }

        // Handle export
        if ($request->filled('export') && $request->export === 'excel') {
            return $this->exportToExcel($query->get());
        }

        // Get statistics for cards (without pagination)
        $statsQuery = clone $query;
        $allShipments = $statsQuery->get();

        // Statistics
        $data['totalShipments'] = $allShipments->count();
        $data['pendingShipments'] = $allShipments->where('isprint', 'N')->count();
        $data['processedShipments'] = $allShipments->where('isprint', 'Y')->count();
        $data['activeCouriers'] = $allShipments->pluck('courier_service')->unique()->filter()->count();

        // Get all shipments for filter dropdowns
        $allShipmentsForFilters = KurirShipmentRequest::where('item_description', 'LIKE', '%by.u store_%')->get();
        $data['allShipments'] = $allShipmentsForFilters;

        // Pagination
        $perPage = $request->filled('per_page') ? $request->per_page : 25;
        $shipmentRequest = $query->latest('request_datetime')->paginate($perPage);

        $data['shipmentRequest'] = $shipmentRequest;
        return view('admin.dashboard', $data);
    }

    private function exportToExcel($shipmentRequests)
    {
        $filename = 'shipment_requests_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($shipmentRequests) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Request Date',
                'Courier Service',
                'Shipper Name',
                'Shipper Address',
                'Receiver Name',
                'Receiver Address',
                'AWB',
                'Item Description',
                'Status'
            ]);

            // CSV data
            foreach ($shipmentRequests as $request) {
                fputcsv($file, [
                    $request->request_datetime,
                    $request->courier_service,
                    $request->shipper_name,
                    $request->shipper_address,
                    $request->receiver_name,
                    $request->receiver_address,
                    $request->awb,
                    $request->item_description,
                    $request->isprint == 'Y' ? 'Processed' : 'Pending'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function processShipment(Request $request, $id)
    {
        try {
            $shipmentRequest = KurirShipmentRequest::findOrFail($id);
            $shipmentRequest->isprint = 'Y';
            $shipmentRequest->save();

            return response()->json([
                'success' => true,
                'message' => 'Shipment processed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process shipment: ' . $e->getMessage()
            ], 500);
        }
    }

    public function indexHistory(Request $request)
    {
        $user = AppUser::join('warehouse_location', 'app_user.warehouse_id', 'warehouse_location.warehouse_id')
                        ->where('app_user.user_id', Auth::id())
                        ->select('app_user.*', 'warehouse_location.warehouse_type')->first();
        
        if($user->warehouse_type == 'DUPLICATE') {
            $query = KurirShipmentRequest::where('item_description', 'LIKE', '%by.u store_%')->where('isprint', 'Y');
        } else {
            $query = KurirShipmentRequest::where('warehouse_id', $user->warehouse_id)->where('item_description', 'LIKE', '%by.u store_%')->where('isprint', 'Y');
        }

        if ($request->filled('daterange')) {
            $dates = explode(' - ', $request->daterange);
            $startDate = Carbon::createFromFormat('M d, Y', $dates[0])->startOfDay();
            $endDate = Carbon::createFromFormat('M d, Y', $dates[1])->endOfDay();
            $query->whereBetween('request_datetime', [$startDate, $endDate]);
        }

        $shipmentRequest = $query->latest('request_datetime')->get();

        $data['shipmentRequest'] = $shipmentRequest;
        return view('admin.shipment-request.history', $data);
    }

    public function showResi($id) {
        $shipmentRequest = KurirShipmentRequest::find($id);
        $shipmentRequest->isprint = 'Y';
        $shipmentRequest->save();
        $shipmentRequest->item_description = substr(strstr($shipmentRequest->item_description, 'by.u store_'), strlen('by.u store_'));
        $shipmentRequest->request_datetime = Carbon::parse($shipmentRequest->request_datetime)->isoFormat('D MMM, YYYY H:mm');
        $barcode = new DNS1D();
        $barcodeImage = $barcode->getBarcodePNG($shipmentRequest->awb, 'C39');
        $data['barcodeImageData'] = base64_encode($barcodeImage);
        $data['shipmentRequest'] = $shipmentRequest;
        return view('admin.template-resi.resi', $data);
    }

}
