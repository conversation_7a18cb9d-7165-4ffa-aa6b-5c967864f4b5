<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMatchGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('match_groups', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->integer('school_id')->nullable();
            $table->integer('play_count')->nullable();
            $table->integer('win_count')->nullable();
            $table->integer('lose_count')->nullable();
            $table->integer('draw_count')->nullable();
            $table->integer('point_total')->nullable();
            $table->string('group');
            $table->string('category');
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('match_groups');
    }
}
