<?php

namespace App\Services;

interface MfaServiceInterface
{
    /**
     * Request OTP from external MFA service
     *
     * @param string $email User email
     * @param string $phone User phone number (optional)
     * @return array Response containing success status and message
     */
    public function requestOtp(string $email, string $phone = null): array;

    /**
     * Validate OTP with external MFA service
     *
     * @param string $email User email
     * @param string $otp OTP code to validate
     * @return array Response containing validation result
     */
    public function validateOtp(string $email, string $otp): array;

    /**
     * Generate session token for MFA verification
     *
     * @param string $email User email
     * @return string Session token
     */
    public function generateMfaToken(string $email): string;

    /**
     * Validate MFA session token
     *
     * @param string $token MFA session token
     * @return bool True if token is valid
     */
    public function validateMfaToken(string $token): bool;
}
