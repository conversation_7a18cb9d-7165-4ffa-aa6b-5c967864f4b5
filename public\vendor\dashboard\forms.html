﻿<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="">
        <meta name="author" content="">
        <title>Forms - SB Admin Pro</title>
        <link href="npm/litepicker/dist/css/litepicker.css" rel="stylesheet">
        <link href="css/styles.css" rel="stylesheet">
        <link rel="icon" type="image/x-icon" href="assets/img/favicon.png">
        <script data-search-pseudo-elements="" defer="" src="ajax/libs/font-awesome/6.3.0/js/all.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/feather-icons/4.29.0/feather.min.js" crossorigin="anonymous"></script>
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i data-feather="menu"></i></button>
            <!-- Navbar Brand-->
            <!-- * * Tip * * You can use text or an image for your navbar brand.-->
            <!-- * * * * * * When using an image, we recommend the SVG format.-->
            <!-- * * * * * * Dimensions: Maximum height: 32px, maximum width: 240px-->
            <a class="navbar-brand pe-3 ps-4 ps-lg-2" href="index.html">SB Admin Pro</a>
            <!-- Navbar Search Input-->
            <!-- * * Note: * * Visible only on and above the lg breakpoint-->
            <form class="form-inline me-auto d-none d-lg-block me-3">
                <div class="input-group input-group-joined input-group-solid">
                    <input class="form-control pe-0" type="search" placeholder="Search" aria-label="Search">
                    <div class="input-group-text"><i data-feather="search"></i></div>
                </div>
            </form>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto">
                <!-- Documentation Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-md-block me-3">
                    <a class="nav-link dropdown-toggle" id="navbarDropdownDocs" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <div class="fw-500">Documentation</div>
                        <i class="fas fa-chevron-right dropdown-arrow"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end py-0 me-sm-n15 me-lg-0 o-hidden animated--fade-in-up" aria-labelledby="navbarDropdownDocs">
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="book"></i></div>
                            <div>
                                <div class="small text-gray-500">Documentation</div>
                                Usage instructions and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/components" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="code"></i></div>
                            <div>
                                <div class="small text-gray-500">Components</div>
                                Code snippets and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/changelog" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="file-text"></i></div>
                            <div>
                                <div class="small text-gray-500">Changelog</div>
                                Updates and changes
                            </div>
                        </a>
                    </div>
                </li>
                <!-- Navbar Search Dropdown-->
                <!-- * * Note: * * Visible only below the lg breakpoint-->
                <li class="nav-item dropdown no-caret me-3 d-lg-none">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="searchDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="search"></i></a>
                    <!-- Dropdown - Search-->
                    <div class="dropdown-menu dropdown-menu-end p-3 shadow animated--fade-in-up" aria-labelledby="searchDropdown">
                        <form class="form-inline me-auto w-100">
                            <div class="input-group input-group-joined input-group-solid">
                                <input class="form-control pe-0" type="text" placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2">
                                <div class="input-group-text"><i data-feather="search"></i></div>
                            </div>
                        </form>
                    </div>
                </li>
                <!-- Alerts Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownAlerts" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="bell"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownAlerts">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="bell"></i>
                            Alerts Center
                        </h6>
                        <!-- Example Alert 1-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-warning"><i data-feather="activity"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 29, 2021</div>
                                <div class="dropdown-notifications-item-content-text">This is an alert message. It's nothing serious, but it requires your attention.</div>
                            </div>
                        </a>
                        <!-- Example Alert 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-info"><i data-feather="bar-chart"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 22, 2021</div>
                                <div class="dropdown-notifications-item-content-text">A new monthly report is ready. Click here to view!</div>
                            </div>
                        </a>
                        <!-- Example Alert 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-danger"><i class="fas fa-exclamation-triangle"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 8, 2021</div>
                                <div class="dropdown-notifications-item-content-text">Critical system failure, systems shutting down.</div>
                            </div>
                        </a>
                        <!-- Example Alert 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-success"><i data-feather="user-plus"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 2, 2021</div>
                                <div class="dropdown-notifications-item-content-text">New user request. Woody has requested access to the organization.</div>
                            </div>
                        </a>
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">View All Alerts</a>
                    </div>
                </li>
                <!-- Messages Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownMessages" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="mail"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownMessages">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="mail"></i>
                            Message Center
                        </h6>
                        <!-- Example Message 1  -->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-2.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Thomas Wilcox · 58m</div>
                            </div>
                        </a>
                        <!-- Example Message 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-3.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Emily Fowler · 2d</div>
                            </div>
                        </a>
                        <!-- Example Message 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-4.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Marshall Rosencrantz · 3d</div>
                            </div>
                        </a>
                        <!-- Example Message 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-5.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Colby Newton · 3d</div>
                            </div>
                        </a>
                        <!-- Footer Link-->
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">Read All Messages</a>
                    </div>
                </li>
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="assets/img/illustrations/profiles/profile-1.png"></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="assets/img/illustrations/profiles/profile-1.png">
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">Valerie Luna</div>
                                <div class="dropdown-user-details-email"><a href="cdn-cgi/l/email-protection.html" class="__cf_email__" data-cfemail="ef99839a818eaf8e8083c18c8082">[email&#160;protected]</a></div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <!-- Sidenav Menu Heading (Account)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <div class="sidenav-menu-heading d-sm-none">Account</div>
                            <!-- Sidenav Link (Alerts)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="bell"></i></div>
                                Alerts
                                <span class="badge bg-warning-soft text-warning ms-auto">4 New!</span>
                            </a>
                            <!-- Sidenav Link (Messages)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="mail"></i></div>
                                Messages
                                <span class="badge bg-success-soft text-success ms-auto">2 New!</span>
                            </a>
                            <!-- Sidenav Menu Heading (Core)-->
                            <div class="sidenav-menu-heading">Core</div>
                            <!-- Sidenav Accordion (Dashboard)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseDashboards" aria-expanded="false" aria-controls="collapseDashboards">
                                <div class="nav-link-icon"><i data-feather="activity"></i></div>
                                Dashboards
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseDashboards" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPages">
                                    <a class="nav-link" href="dashboard-1.html">
                                        Default
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dashboard-2.html">Multipurpose</a>
                                    <a class="nav-link" href="dashboard-3.html">Affiliate</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Custom)-->
                            <div class="sidenav-menu-heading">Custom</div>
                            <!-- Sidenav Accordion (Pages)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapsePages" aria-expanded="false" aria-controls="collapsePages">
                                <div class="nav-link-icon"><i data-feather="grid"></i></div>
                                Pages
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapsePages" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesMenu">
                                    <!-- Nested Sidenav Accordion (Pages -> Account)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAccount" aria-expanded="false" aria-controls="pagesCollapseAccount">
                                        Account
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAccount" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="account-profile.html">Profile</a>
                                            <a class="nav-link" href="account-billing.html">Billing</a>
                                            <a class="nav-link" href="account-security.html">Security</a>
                                            <a class="nav-link" href="account-notifications.html">Notifications</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Authentication)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuth" aria-expanded="false" aria-controls="pagesCollapseAuth">
                                        Authentication
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAuth" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesAuth">
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Basic)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthBasic" aria-expanded="false" aria-controls="pagesCollapseAuthBasic">
                                                Basic
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthBasic" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-basic.html">Login</a>
                                                    <a class="nav-link" href="auth-register-basic.html">Register</a>
                                                    <a class="nav-link" href="auth-password-basic.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Social)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthSocial" aria-expanded="false" aria-controls="pagesCollapseAuthSocial">
                                                Social
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthSocial" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-social.html">Login</a>
                                                    <a class="nav-link" href="auth-register-social.html">Register</a>
                                                    <a class="nav-link" href="auth-password-social.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Error)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseError" aria-expanded="false" aria-controls="pagesCollapseError">
                                        Error
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseError" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="error-400.html">400 Error</a>
                                            <a class="nav-link" href="error-401.html">401 Error</a>
                                            <a class="nav-link" href="error-403.html">403 Error</a>
                                            <a class="nav-link" href="error-404-1.html">404 Error 1</a>
                                            <a class="nav-link" href="error-404-2.html">404 Error 2</a>
                                            <a class="nav-link" href="error-500.html">500 Error</a>
                                            <a class="nav-link" href="error-503.html">503 Error</a>
                                            <a class="nav-link" href="error-504.html">504 Error</a>
                                        </nav>
                                    </div>
                                    <a class="nav-link" href="pricing.html">Pricing</a>
                                    <a class="nav-link" href="invoice.html">Invoice</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Applications)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseApps" aria-expanded="false" aria-controls="collapseApps">
                                <div class="nav-link-icon"><i data-feather="globe"></i></div>
                                Applications
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseApps" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavAppsMenu">
                                    <!-- Nested Sidenav Accordion (Apps -> Knowledge Base)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseKnowledgeBase" aria-expanded="false" aria-controls="appsCollapseKnowledgeBase">
                                        Knowledge Base
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseKnowledgeBase" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="knowledge-base-home-1.html">Home 1</a>
                                            <a class="nav-link" href="knowledge-base-home-2.html">Home 2</a>
                                            <a class="nav-link" href="knowledge-base-category.html">Category</a>
                                            <a class="nav-link" href="knowledge-base-article.html">Article</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> User Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseUserManagement" aria-expanded="false" aria-controls="appsCollapseUserManagement">
                                        User Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseUserManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="user-management-list.html">Users List</a>
                                            <a class="nav-link" href="user-management-edit-user.html">Edit User</a>
                                            <a class="nav-link" href="user-management-add-user.html">Add User</a>
                                            <a class="nav-link" href="user-management-groups-list.html">Groups List</a>
                                            <a class="nav-link" href="user-management-org-details.html">Organization Details</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> Posts Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapsePostsManagement" aria-expanded="false" aria-controls="appsCollapsePostsManagement">
                                        Posts Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapsePostsManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="blog-management-posts-list.html">Posts List</a>
                                            <a class="nav-link" href="blog-management-create-post.html">Create Post</a>
                                            <a class="nav-link" href="blog-management-edit-post.html">Edit Post</a>
                                            <a class="nav-link" href="blog-management-posts-admin.html">Posts Admin</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Flows)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseFlows" aria-expanded="false" aria-controls="collapseFlows">
                                <div class="nav-link-icon"><i data-feather="repeat"></i></div>
                                Flows
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseFlows" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="multi-tenant-select.html">Multi-Tenant Registration</a>
                                    <a class="nav-link" href="wizard.html">Wizard</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (UI Toolkit)-->
                            <div class="sidenav-menu-heading">UI Toolkit</div>
                            <!-- Sidenav Accordion (Layout)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayouts" aria-expanded="false" aria-controls="collapseLayouts">
                                <div class="nav-link-icon"><i data-feather="layout"></i></div>
                                Layout
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseLayouts" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavLayout">
                                    <!-- Nested Sidenav Accordion (Layout -> Navigation)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutSidenavVariations" aria-expanded="false" aria-controls="collapseLayoutSidenavVariations">
                                        Navigation
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutSidenavVariations" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-static.html">Static Sidenav</a>
                                            <a class="nav-link" href="layout-dark.html">Dark Sidenav</a>
                                            <a class="nav-link" href="layout-rtl.html">RTL Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Container Options)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutContainers" aria-expanded="false" aria-controls="collapseLayoutContainers">
                                        Container Options
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutContainers" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-boxed.html">Boxed Layout</a>
                                            <a class="nav-link" href="layout-fluid.html">Fluid Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Page Headers)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsPageHeaders" aria-expanded="false" aria-controls="collapseLayoutsPageHeaders">
                                        Page Headers
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsPageHeaders" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="header-simplified.html">Simplified</a>
                                            <a class="nav-link" href="header-compact.html">Compact</a>
                                            <a class="nav-link" href="header-overlap.html">Content Overlap</a>
                                            <a class="nav-link" href="header-breadcrumbs.html">Breadcrumbs</a>
                                            <a class="nav-link" href="header-light.html">Light</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Starter Layouts)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsStarterTemplates" aria-expanded="false" aria-controls="collapseLayoutsStarterTemplates">
                                        Starter Layouts
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsStarterTemplates" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="starter-default.html">Default</a>
                                            <a class="nav-link" href="starter-minimal.html">Minimal</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Components)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseComponents" aria-expanded="false" aria-controls="collapseComponents">
                                <div class="nav-link-icon"><i data-feather="package"></i></div>
                                Components
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseComponents" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="alerts.html">Alerts</a>
                                    <a class="nav-link" href="avatars.html">Avatars</a>
                                    <a class="nav-link" href="badges.html">Badges</a>
                                    <a class="nav-link" href="buttons.html">Buttons</a>
                                    <a class="nav-link" href="cards.html">
                                        Cards
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dropdowns.html">Dropdowns</a>
                                    <a class="nav-link" href="forms.html">
                                        Forms
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="modals.html">Modals</a>
                                    <a class="nav-link" href="navigation.html">Navigation</a>
                                    <a class="nav-link" href="progress.html">Progress</a>
                                    <a class="nav-link" href="step.html">Step</a>
                                    <a class="nav-link" href="timeline.html">Timeline</a>
                                    <a class="nav-link" href="toasts.html">Toasts</a>
                                    <a class="nav-link" href="tooltips.html">Tooltips</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Utilities)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseUtilities" aria-expanded="false" aria-controls="collapseUtilities">
                                <div class="nav-link-icon"><i data-feather="tool"></i></div>
                                Utilities
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseUtilities" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="animations.html">Animations</a>
                                    <a class="nav-link" href="background.html">Background</a>
                                    <a class="nav-link" href="borders.html">Borders</a>
                                    <a class="nav-link" href="lift.html">Lift</a>
                                    <a class="nav-link" href="shadows.html">Shadows</a>
                                    <a class="nav-link" href="typography.html">Typography</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Addons)-->
                            <div class="sidenav-menu-heading">Plugins</div>
                            <!-- Sidenav Link (Charts)-->
                            <a class="nav-link" href="charts.html">
                                <div class="nav-link-icon"><i data-feather="bar-chart"></i></div>
                                Charts
                            </a>
                            <!-- Sidenav Link (Tables)-->
                            <a class="nav-link" href="tables.html">
                                <div class="nav-link-icon"><i data-feather="filter"></i></div>
                                Tables
                            </a>
                        </div>
                    </div>
                    <!-- Sidenav Footer-->
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title">Valerie Luna</div>
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-dark bg-gradient-primary-to-secondary pb-10">
                        <div class="container-xl px-4">
                            <div class="page-header-content pt-4">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mt-4">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i data-feather="edit-3"></i></div>
                                            Forms
                                        </h1>
                                        <div class="page-header-subtitle">Dynamic form components to give your users informative and intuitive inputs</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <!-- Main page content-->
                    <div class="container-xl px-4 mt-n10">
                        <div class="row">
                            <div class="col-lg-9">
                                <!-- Default Bootstrap Form Controls-->
                                <div id="default">
                                    <div class="card mb-4">
                                        <div class="card-header">Default Bootstrap Form Controls</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <form>
                                                        <div class="mb-3">
                                                            <label for="exampleFormControlInput1">Email address</label>
                                                            <input class="form-control" id="exampleFormControlInput1" type="email" placeholder="<EMAIL>">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="exampleFormControlSelect1">Example select</label>
                                                            <select class="form-control" id="exampleFormControlSelect1">
                                                                <option>1</option>
                                                                <option>2</option>
                                                                <option>3</option>
                                                                <option>4</option>
                                                                <option>5</option>
                                                            </select>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="exampleFormControlSelect2">Example multiple select</label>
                                                            <select class="form-control" id="exampleFormControlSelect2" multiple="">
                                                                <option>1</option>
                                                                <option>2</option>
                                                                <option>3</option>
                                                                <option>4</option>
                                                                <option>5</option>
                                                            </select>
                                                        </div>
                                                        <div class="mb-0">
                                                            <label for="exampleFormControlTextarea1">Example textarea</label>
                                                            <textarea class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
                                                        </div>
                                                    </form>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="formsDefaultTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="formsDefaultHtmlTab" data-bs-toggle="tab" href="#formsDefaultHtml" role="tab" aria-controls="formsDefaultHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="formsDefaultPugTab" data-bs-toggle="tab" href="#formsDefaultPug" role="tab" aria-controls="formsDefaultPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="formsDefaultHtml" role="tabpanel" aria-labelledby="formsDefaultHtmlTab">
                                                            <pre class="language-markup"><code><script data-cfasync="false" src="cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script type="text/plain"><form>
    <div class="mb-3"><label for="exampleFormControlInput1">Email address</label><input class="form-control" id="exampleFormControlInput1" type="email" placeholder="<EMAIL>"></div>
    <div class="mb-3">
        <label for="exampleFormControlSelect1">Example select</label><select class="form-control" id="exampleFormControlSelect1">
            <option>1</option>
            <option>2</option>
            <option>3</option>
            <option>4</option>
            <option>5</option>
        </select>
    </div>
    <div class="mb-3">
        <label for="exampleFormControlSelect2">Example multiple select</label><select class="form-control" id="exampleFormControlSelect2" multiple="">
            <option>1</option>
            <option>2</option>
            <option>3</option>
            <option>4</option>
            <option>5</option>
        </select>
    </div>
    <div class="mb-0"><label for="exampleFormControlTextarea1">Example textarea</label><textarea class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea></div>
</form></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="formsDefaultPug" role="tabpanel" aria-labelledby="formsDefaultPugTab">
                                                            <pre class="language-pug"><code>form
    .mb-3
        label(for='exampleFormControlInput1') Email address
        input#exampleFormControlInput1.form-control(type='email', placeholder='<a href="cdn-cgi/l/email-protection.html" class="__cf_email__" data-cfemail="7a141b171f3a1f021b170a161f54191517">[email&#160;protected]</a>')
    .mb-3
        label(for='exampleFormControlSelect1') Example select
        select#exampleFormControlSelect1.form-control
            option 1
            option 2
            option 3
            option 4
            option 5
    .mb-3
        label(for='exampleFormControlSelect2') Example multiple select
        select#exampleFormControlSelect2.form-control(multiple='')
            option 1
            option 2
            option 3
            option 4
            option 5
    .mb-0
        label(for='exampleFormControlTextarea1') Example textarea
        textarea#exampleFormControlTextarea1.form-control(rows='3')
</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">The default Bootstrap form control states have been restyled to fit the SB Admin Pro theme. The style has changed, but the markup is identical.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Solid Form Controls-->
                                <div id="solid">
                                    <div class="card mb-4">
                                        <div class="card-header">Custom Solid Form Controls</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <form>
                                                        <div class="mb-3">
                                                            <label for="exampleFormControlInput1">Email address</label>
                                                            <input class="form-control form-control-solid" id="exampleFormControlInput1" type="email" placeholder="<EMAIL>">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="exampleFormControlSelect1">Example select</label>
                                                            <select class="form-control form-control-solid" id="exampleFormControlSelect1">
                                                                <option>1</option>
                                                                <option>2</option>
                                                                <option>3</option>
                                                                <option>4</option>
                                                                <option>5</option>
                                                            </select>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="exampleFormControlSelect2">Example multiple select</label>
                                                            <select class="form-control form-control-solid" id="exampleFormControlSelect2" multiple="">
                                                                <option>1</option>
                                                                <option>2</option>
                                                                <option>3</option>
                                                                <option>4</option>
                                                                <option>5</option>
                                                            </select>
                                                        </div>
                                                        <div class="mb-0">
                                                            <label for="exampleFormControlTextarea1">Example textarea</label>
                                                            <textarea class="form-control form-control-solid" id="exampleFormControlTextarea1" rows="3"></textarea>
                                                        </div>
                                                    </form>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="formsSolidTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="formsSolidHtmlTab" data-bs-toggle="tab" href="#formsSolidHtml" role="tab" aria-controls="formsSolidHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="formsSolidPugTab" data-bs-toggle="tab" href="#formsSolidPug" role="tab" aria-controls="formsSolidPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="formsSolidHtml" role="tabpanel" aria-labelledby="formsSolidHtmlTab">
                                                            <pre class="language-markup"><code><script data-cfasync="false" src="cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script type="text/plain"><form>
    <div class="mb-3"><label for="exampleFormControlInput1">Email address</label><input class="form-control form-control-solid" id="exampleFormControlInput1" type="email" placeholder="<EMAIL>"></div>
    <div class="mb-3">
        <label for="exampleFormControlSelect1">Example select</label><select class="form-control form-control-solid" id="exampleFormControlSelect1">
            <option>1</option>
            <option>2</option>
            <option>3</option>
            <option>4</option>
            <option>5</option>
        </select>
    </div>
    <div class="mb-3">
        <label for="exampleFormControlSelect2">Example multiple select</label><select class="form-control form-control-solid" id="exampleFormControlSelect2" multiple="">
            <option>1</option>
            <option>2</option>
            <option>3</option>
            <option>4</option>
            <option>5</option>
        </select>
    </div>
    <div class="mb-0"><label for="exampleFormControlTextarea1">Example textarea</label><textarea class="form-control form-control-solid" id="exampleFormControlTextarea1" rows="3"></textarea></div>
</form></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="formsSolidPug" role="tabpanel" aria-labelledby="formsSolidPugTab">
                                                            <pre class="language-pug"><code>form
    .mb-3
        label(for='exampleFormControlInput1') Email address
        input#exampleFormControlInput1.form-control.form-control-solid(type='email', placeholder='<a href="cdn-cgi/l/email-protection.html" class="__cf_email__" data-cfemail="abc5cac6ceebced3cac6dbc7ce85c8c4c6">[email&#160;protected]</a>')
    .mb-3
        label(for='exampleFormControlSelect1') Example select
        select#exampleFormControlSelect1.form-control.form-control-solid
            option 1
            option 2
            option 3
            option 4
            option 5
    .mb-3
        label(for='exampleFormControlSelect2') Example multiple select
        select#exampleFormControlSelect2.form-control.form-control-solid(multiple='')
            option 1
            option 2
            option 3
            option 4
            option 5
    .mb-0
        label(for='exampleFormControlTextarea1') Example textarea
        textarea#exampleFormControlTextarea1.form-control.form-control-solid(rows='3')
</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Custom solid form controls have been developed as an alternative to the default form styling. Use them by appending the
                                                    <code>.form-control-solid</code>
                                                    to the normal
                                                    <code>.form-control</code>
                                                    class.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Custom Checkboxes and Radio-->
                                <div id="checkbox">
                                    <div class="card mb-4">
                                        <div class="card-header">Custom Checkboxes &amp; Radio</div>
                                        <div class="card-body">
                                            <h6 class="small text-muted fw-500">Bootstrap Custom Checkboxes:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="form-check">
                                                        <input class="form-check-input" id="flexCheckDefault" type="checkbox" value="">
                                                        <label class="form-check-label" for="flexCheckDefault">Default checkbox</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" id="flexCheckChecked" type="checkbox" value="" checked="">
                                                        <label class="form-check-label" for="flexCheckChecked">Checked checkbox</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" id="flexCheckDisabled" type="checkbox" value="" disabled="">
                                                        <label class="form-check-label" for="flexCheckDisabled">Disabled checkbox</label>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="formsCheckboxesTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="formsCheckboxesHtmlTab" data-bs-toggle="tab" href="#formsCheckboxesHtml" role="tab" aria-controls="formsCheckboxesHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="formsCheckboxesPugTab" data-bs-toggle="tab" href="#formsCheckboxesPug" role="tab" aria-controls="formsCheckboxesPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="formsCheckboxesHtml" role="tabpanel" aria-labelledby="formsCheckboxesHtmlTab">
                                                            <pre class="language-markup"><code><script data-cfasync="false" src="cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script type="text/plain"><div class="form-check">
    <input class="form-check-input" id="flexCheckDefault" type="checkbox" value="">
    <label class="form-check-label" for="flexCheckDefault">Default checkbox</label>
</div>
<div class="form-check">
    <input class="form-check-input" id="flexCheckChecked" type="checkbox" value="" checked">
    <label class="form-check-label" for="flexCheckChecked">Checked checkbox</label>
</div>
<div class="form-check">
    <input class="form-check-input" id="flexCheckDisabled" type="checkbox" value="" disabled>
    <label class="form-check-label" for="flexCheckDisabled">Disabled checkbox</label>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="formsCheckboxesPug" role="tabpanel" aria-labelledby="formsCheckboxesPugTab">
                                                            <pre class="language-pug"><code>.form-check
    input#flexCheckDefault.form-check-input(type='checkbox', value='')
    label.form-check-label(for='flexCheckDefault')
        | Default checkbox
.form-check
    input#flexCheckChecked.form-check-input(type='checkbox', value='', checked)
    label.form-check-label(for='flexCheckChecked')
        | Checked checkbox
.form-check
    input#flexCheckDisabled.form-check-input(type='checkbox', value='', disabled)
    label.form-check-label(for='flexCheckDisabled')
        | Disabled checkbox</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Bootstrap's custom checkbox styling has been modified for the SB Admin Pro theme.</div>
                                            </div>
                                            <h6 class="small text-muted fw-500">Bootstrap Custom Radio:</h6>
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="form-check">
                                                        <input class="form-check-input" id="flexRadioDefault1" type="radio" name="flexRadioDefault">
                                                        <label class="form-check-label" for="flexRadioDefault1">Default radio</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" id="flexRadioDefault2" type="radio" name="flexRadioDefault" checked="">
                                                        <label class="form-check-label" for="flexRadioDefault2">Default checked radio</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" id="flexRadioDefault3" type="radio" name="flexRadioDefault" disabled="">
                                                        <label class="form-check-label" for="flexRadioDefault3">Default disabled radio</label>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="formsRadioTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="formsRadioHtmlTab" data-bs-toggle="tab" href="#formsRadioHtml" role="tab" aria-controls="formsRadioHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="formsRadioPugTab" data-bs-toggle="tab" href="#formsRadioPug" role="tab" aria-controls="formsRadioPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="formsRadioHtml" role="tabpanel" aria-labelledby="formsRadioHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="form-check">
    <input class="form-check-input" id="flexRadioDefault1" type="radio" name="flexRadioDefault">
    <label class="form-check-label" for="flexRadioDefault1">Default radio</label>
</div>
<div class="form-check">
    <input class="form-check-input" id="flexRadioDefault2" type="radio" name="flexRadioDefault" checked">
    <label class="form-check-label" for="flexRadioDefault2">Default checked radio</label>
</div>
<div class="form-check">
    <input class="form-check-input" id="flexRadioDefault3" type="radio" name="flexRadioDefault" disabled>
    <label class="form-check-label" for="flexRadioDefault3">Default disabled radio</label>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="formsRadioPug" role="tabpanel" aria-labelledby="formsRadioPugTab">
                                                            <pre class="language-pug"><code>.form-check
    input#flexRadioDefault1.form-check-input(type='radio', name='flexRadioDefault')
    label.form-check-label(for='flexRadioDefault1')
        | Default radio
.form-check
    input#flexRadioDefault2.form-check-input(type='radio', name='flexRadioDefault', checked)
    label.form-check-label(for='flexRadioDefault2')
        | Default checked radio
.form-check
    input#flexRadioDefault3.form-check-input(type='radio', name='flexRadioDefault', disabled)
    label.form-check-label(for='flexRadioDefault3')
        | Default disabled radio</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Bootstrap's custom radio styling has also been modified for the SB Admin Pro theme.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Solid Checkbox and Radio Styling-->
                                <div id="checkboxSolid">
                                    <div class="card mb-4">
                                        <div class="card-header">Solid Checkboxes &amp; Radio</div>
                                        <div class="card-body">
                                            <h6 class="small text-muted fw-500">Custom Solid Checkboxes:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="form-check form-check-solid">
                                                        <input class="form-check-input" id="flexCheckSolidDefault" type="checkbox" value="">
                                                        <label class="form-check-label" for="flexCheckSolidDefault">Default checkbox</label>
                                                    </div>
                                                    <div class="form-check form-check-solid">
                                                        <input class="form-check-input" id="flexCheckSolidChecked" type="checkbox" value="" checked="">
                                                        <label class="form-check-label" for="flexCheckSolidChecked">Checked checkbox</label>
                                                    </div>
                                                    <div class="form-check form-check-solid">
                                                        <input class="form-check-input" id="flexCheckSolidDisabled" type="checkbox" value="" disabled="">
                                                        <label class="form-check-label" for="flexCheckSolidDisabled">Disabled checkbox</label>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="formsCheckboxesSolidTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="formsCheckboxesSolidHtmlTab" data-bs-toggle="tab" href="#formsCheckboxesSolidHtml" role="tab" aria-controls="formsCheckboxesSolidHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="formsCheckboxesSolidPugTab" data-bs-toggle="tab" href="#formsCheckboxesSolidPug" role="tab" aria-controls="formsCheckboxesSolidPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="formsCheckboxesSolidHtml" role="tabpanel" aria-labelledby="formsCheckboxesSolidHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="form-check form-check-solid">
    <input class="form-check-input" id="flexCheckSolidDefault" type="checkbox" value="">
    <label class="form-check-label" for="flexCheckSolidDefault">Default checkbox</label>
</div>
<div class="form-check form-check-solid">
    <input class="form-check-input" id="flexCheckSolidChecked" type="checkbox" value="" checked">
    <label class="form-check-label" for="flexCheckSolidChecked">Checked checkbox</label>
</div>
<div class="form-check form-check-solid">
    <input class="form-check-input" id="flexCheckSolidDisabled" type="checkbox" value="" disabled>
    <label class="form-check-label" for="flexCheckSolidDisabled">Disabled checkbox</label>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="formsCheckboxesSolidPug" role="tabpanel" aria-labelledby="formsCheckboxesSolidPugTab">
                                                            <pre class="language-pug"><code>.form-check.form-check-solid
    input#flexCheckSolidDefault.form-check-input(type='checkbox', value='')
    label.form-check-label(for='flexCheckSolidDefault')
        | Default checkbox
.form-check.form-check-solid
    input#flexCheckSolidChecked.form-check-input(type='checkbox', value='', checked)
    label.form-check-label(for='flexCheckSolidChecked')
        | Checked checkbox
.form-check.form-check-solid
    input#flexCheckSolidDisabled.form-check-input(type='checkbox', value='', disabled)
    label.form-check-label(for='flexCheckSolidDisabled')
        | Disabled checkbox</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The
                                                    <code>.custom-control-solid</code>
                                                    can be used with a
                                                    <code>.custom-control</code>
                                                    class to get a solid styling for custom checkboxes.
                                                </div>
                                            </div>
                                            <h6 class="small text-muted fw-500">Custom Solid Radio:</h6>
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="form-check form-check-solid">
                                                        <input class="form-check-input" id="flexRadioSolid1" type="radio" name="flexRadioSolid">
                                                        <label class="form-check-label" for="flexRadioSolid1">Default radio</label>
                                                    </div>
                                                    <div class="form-check form-check-solid">
                                                        <input class="form-check-input" id="flexRadioSolid2" type="radio" name="flexRadioSolid" checked="">
                                                        <label class="form-check-label" for="flexRadioSolid2">Default checked radio</label>
                                                    </div>
                                                    <div class="form-check form-check-solid">
                                                        <input class="form-check-input" id="flexRadioSolid3" type="radio" name="flexRadioSolid" disabled="">
                                                        <label class="form-check-label" for="flexRadioSolid3">Default disabled radio</label>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="formsRadioSolidTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="formsRadioSolidHtmlTab" data-bs-toggle="tab" href="#formsRadioSolidHtml" role="tab" aria-controls="formsRadioSolidHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="formsRadioSolidPugTab" data-bs-toggle="tab" href="#formsRadioSolidPug" role="tab" aria-controls="formsRadioSolidPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="formsRadioSolidHtml" role="tabpanel" aria-labelledby="formsRadioSolidHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="form-check form-check-solid">
    <input class="form-check-input" id="flexRadioSolid1" type="radio" name="flexRadioSolid">
    <label class="form-check-label" for="flexRadioSolid1">Default radio</label>
</div>
<div class="form-check form-check-solid">
    <input class="form-check-input" id="flexRadioSolid2" type="radio" name="flexRadioSolid" checked">
    <label class="form-check-label" for="flexRadioSolid2">Default checked radio</label>
</div>
<div class="form-check form-check-solid">
    <input class="form-check-input" id="flexRadioSolid3" type="radio" name="flexRadioSolid" disabled>
    <label class="form-check-label" for="flexRadioSolid3">Default disabled radio</label>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="formsRadioSolidPug" role="tabpanel" aria-labelledby="formsRadioSolidPugTab">
                                                            <pre class="language-pug"><code>.form-check.form-check-solid
    input#flexRadioSolid1.form-check-input(type='radio', name='flexRadioSolid')
    label.form-check-label(for='flexRadioSolid1')
        | Default radio
.form-check.form-check-solid
    input#flexRadioSolid2.form-check-input(type='radio', name='flexRadioSolid', checked)
    label.form-check-label(for='flexRadioSolid2')
        | Default checked radio
.form-check.form-check-solid
    input#flexRadioSolid3.form-check-input(type='radio', name='flexRadioSolid', disabled)
    label.form-check-label(for='flexRadioSolid3')
        | Default disabled radio</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The
                                                    <code>.custom-control-solid</code>
                                                    works with custom radios as well.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Joined Input Groups-->
                                <div id="inputGroupJoined">
                                    <div class="card mb-4">
                                        <div class="card-header">Joined Input Group</div>
                                        <div class="card-body">
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <!-- Joined input group append example-->
                                                    <div class="input-group input-group-joined mb-4">
                                                        <input class="form-control pe-0" type="text" placeholder="Input group append..." aria-label="Search">
                                                        <span class="input-group-text"><i data-feather="search"></i></span>
                                                    </div>
                                                    <!-- Joined input group prepend example-->
                                                    <div class="input-group input-group-joined mb-4">
                                                        <span class="input-group-text"><i data-feather="search"></i></span>
                                                        <input class="form-control ps-0" type="text" placeholder="Input group prepend..." aria-label="Search">
                                                    </div>
                                                    <!-- Joined input group solid append example-->
                                                    <div class="input-group input-group-joined input-group-solid mb-4">
                                                        <input class="form-control pe-0" type="text" placeholder="Input group append..." aria-label="Search">
                                                        <span class="input-group-text"><i data-feather="search"></i></span>
                                                    </div>
                                                    <!-- Joined input group solid prepend example-->
                                                    <div class="input-group input-group-joined input-group-solid">
                                                        <span class="input-group-text"><i data-feather="search"></i></span>
                                                        <input class="form-control ps-0" type="text" placeholder="Input group prepend..." aria-label="Search">
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="inputGroupJoinedTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="inputGroupJoinedHtmlTab" data-bs-toggle="tab" href="#inputGroupJoinedHtml" role="tab" aria-controls="inputGroupJoinedHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="inputGroupJoinedPugTab" data-bs-toggle="tab" href="#inputGroupJoinedPug" role="tab" aria-controls="inputGroupJoinedPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="inputGroupJoinedHtml" role="tabpanel" aria-labelledby="inputGroupJoinedHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Joined input group append example-->
<div class="input-group input-group-joined">
    <input class="form-control pe-0" type="text" placeholder="Input group append..." aria-label="Search">
    <span class="input-group-text">
        <i data-feather="search"></i>
    </span>
</div>

<!-- Joined input group prepend example-->
<div class="input-group input-group-joined">
    <span class="input-group-text">
        <i data-feather="search"></i>
    </span>
    <input class="form-control ps-0" type="text" placeholder="Input group prepend..." aria-label="Search">
</div>

<!-- Joined input group solid append example-->
<div class="input-group input-group-joined input-group-solid">
    <input class="form-control pe-0" type="text" placeholder="Input group append..." aria-label="Search">
    <span class="input-group-text">
        <i data-feather="search"></i>
    </span>
</div>

<!-- Joined input group solid prepend example-->
<div class="input-group input-group-joined input-group-solid">
    <span class="input-group-text">
        <i data-feather="search"></i>
    </span>
    <input class="form-control ps-0" type="text" placeholder="Input group prepend..." aria-label="Search">
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="inputGroupJoinedPug" role="tabpanel" aria-labelledby="inputGroupJoinedPugTab">
                                                            <pre class="language-pug"><code>// Joined input group append example
.input-group.input-group-joined
    input.form-control.pe-0(type='text', placeholder='Input group append...', aria-label='Search')
    span.input-group-text
        i(data-feather='search')
// Joined input group prepend example
.input-group.input-group-joined
    span.input-group-text
        i(data-feather='search')
    input.form-control.ps-0(type='text', placeholder='Input group prepend...', aria-label='Search')
// Joined input group solid append example
.input-group.input-group-joined.mb-4.input-group-solid
    input.form-control.pe-0(type='text', placeholder='Input group append...', aria-label='Search')
    span.input-group-text
        i(data-feather='search')
// Joined input group solid prepend example
.input-group.input-group-joined.input-group-solid
    span.input-group-text
        i(data-feather='search')
    input.form-control.ps-0(type='text', placeholder='Input group prepend...', aria-label='Search')</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The joined input group uses the
                                                    <code>:focus-within</code>
                                                    CSS pseudo class to join input group appends and prepends within an input group. Unlike Bootstrap's default input group behavior, these joined input group variations will focus the entire input group when the form control within is selected. This variation also removes the background and border coloring of the append and prepend elements for a smooth, and seamless input group appearance.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Date Range Picker-->
                                <div id="dateRangePicker">
                                    <div class="card card-header-actions mb-4">
                                        <div class="card-header">
                                            Date Range Picker
                                            <span class="badge bg-warning-soft text-warning">Plugin</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <!-- Date Range Picker Example-->
                                                    <div class="input-group input-group-joined" style="width: 16.5rem">
                                                        <span class="input-group-text"><i data-feather="calendar"></i></span>
                                                        <input class="form-control ps-0 pointer" id="litepickerRangePlugin" placeholder="Select date range...">
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="dateRangePickerTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="dateRangePickerHtmlTab" data-bs-toggle="tab" href="#dateRangePickerHtml" role="tab" aria-controls="dateRangePickerHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="dateRangePickerPugTab" data-bs-toggle="tab" href="#dateRangePickerPug" role="tab" aria-controls="dateRangePickerPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="dateRangePickerHtml" role="tabpanel" aria-labelledby="dateRangePickerHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Date Range Picker Example-->
<div class="input-group input-group-joined" style="width: 16.5rem;">
    <span class="input-group-text">
        <i data-feather="calendar"></i>
    </span>
    <input class="form-control ps-0" id="litepickerRangePlugin" placeholder="Select date range..." />
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="dateRangePickerPug" role="tabpanel" aria-labelledby="dateRangePickerPugTab">
                                                            <pre class="language-pug"><code>// Date Range Picker Example
.input-group.input-group-joined(style='width: 16.5rem;')
    span.input-group-text
        i(data-feather='calendar')
    input.form-control.ps-0.pointer#litepickerRangePlugin(placeholder='Select date range...')</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    <p class="mb-0">
                                                        This plugin is powered by a third party dependency called Litepicker. Visit the
                                                        <a href="https://litepicker.com/">Litepicker documentation</a>
                                                        for more information and usage examples.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Bootstrap Docs Link-->
                                <div class="card card-icon mb-4">
                                    <div class="row g-0">
                                        <div class="col-auto card-icon-aside bg-secondary"><i class="me-1 text-white-50 fab fa-bootstrap"></i></div>
                                        <div class="col">
                                            <div class="card-body py-5">
                                                <h5 class="card-title">Bootstrap Documentation Available</h5>
                                                <p class="card-text">Forms are a default component included with the Bootstrap framework. For more information on implementing, modifying, and extending the usage of forms within your project, visit the official Bootstrap forms documentation page.</p>
                                                <a class="btn btn-secondary btn-sm" href="https://getbootstrap.com/docs/4.4/components/forms/" target="_blank">
                                                    <i class="me-1" data-feather="external-link"></i>
                                                    Visit Bootstrap Forms Docs
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Sticky Navigation-->
                            <div class="col-lg-3">
                                <div class="nav-sticky">
                                    <div class="card">
                                        <div class="card-body">
                                            <ul class="nav flex-column" id="stickyNav">
                                                <li class="nav-item"><a class="nav-link" href="#default">Default Form Controls</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#solid">Solid Form Controls</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#checkbox">Default Checkboxes &amp; Radio</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#checkboxSolid">Solid Checkboxes &amp; Radio</a></li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#inputGroupJoined">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            Input Groups
                                                            <span class="badge bg-primary-soft text-primary">New</span>
                                                        </div>
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#dateRangePicker">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            Date Range Picker
                                                            <span class="badge bg-primary-soft text-primary">New</span>
                                                        </div>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container-xl px-4">
                        <div class="row">
                            <div class="col-md-6 small">Copyright © Your Website 2021</div>
                            <div class="col-md-6 text-md-end small">
                                <a href="#!">Privacy Policy</a>
                                ·
                                <a href="#!">Terms &amp; Conditions</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="npm/bootstrap%405.2.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
        <script src="js/scripts.js"></script>
        <script src="ajax/libs/prism/1.17.1/components/prism-core.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/prism/1.17.1/plugins/autoloader/prism-autoloader.min.js" crossorigin="anonymous"></script>
        <script src="npm/litepicker/dist/bundle.js" crossorigin="anonymous"></script>
        <script src="js/litepicker.js"></script>

        <script src="js/sb-customizer.js"></script>
        <sb-customizer project="sb-admin-pro"></sb-customizer>
    <script>(function(){var js = "window['__CF$cv$params']={r:'84b749d32e8289b6',t:'MTcwNjI1NjcxOS44NzYwMDA='};_cpo=document.createElement('script');_cpo.nonce='',_cpo.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js',document.getElementsByTagName('head')[0].appendChild(_cpo);";var _0xh = document.createElement('iframe');_0xh.height = 1;_0xh.width = 1;_0xh.style.position = 'absolute';_0xh.style.top = 0;_0xh.style.left = 0;_0xh.style.border = 'none';_0xh.style.visibility = 'hidden';document.body.appendChild(_0xh);function handler() {var _0xi = _0xh.contentDocument || _0xh.contentWindow.document;if (_0xi) {var _0xj = _0xi.createElement('script');_0xj.innerHTML = js;_0xi.getElementsByTagName('head')[0].appendChild(_0xj);}}if (document.readyState !== 'loading') {handler();} else if (window.addEventListener) {document.addEventListener('DOMContentLoaded', handler);} else {var prev = document.onreadystatechange || function () {};document.onreadystatechange = function (e) {prev(e);if (document.readyState !== 'loading') {document.onreadystatechange = prev;handler();}};}})();</script><script defer="" src="beacon.min.js/v84a3a4012de94ce1a686ba8c167c359c1696973893317" integrity="sha512-euoFGowhlaLqXsPWQ48qSkBSCFs3DPRyiwVu3FjR96cMPx+Fr+gpWRhIafcHwqwCqWS42RZhIudOvEI+Ckf6MA==" data-cf-beacon='{"rayId":"84b749d32e8289b6","b":1,"version":"2024.1.0","token":"6e2c2575ac8f44ed824cef7899ba8463"}' crossorigin="anonymous"></script>
</body>
</html>
