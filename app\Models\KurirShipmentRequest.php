<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class KurirShipmentRequest extends Model
{
    protected $table = 'kurir_shipment_request_ntp';
    protected $primaryKey = 'request_id';
    public $timestamps = false; // Assuming there are no created_at and updated_at columns

    protected $fillable = [
        'request_datetime',
        'warehouse_id',
        'courier_service',
        'shipper_name',
        'shipper_addr',
        'shipper_zipcode',
        'receiver_name',
        'receiver_addr',
        'receiver_phone',
        'receiver_zipcode',
        'orig_code',
        'dest_code',
        'orig_city',
        'awb',
        'branch_code',
        'item_description',
        'item_instruction',
        'bss_order_id',
        'iccid',
        'isprint',
    ];

    // Add relationships or additional methods as needed
}
