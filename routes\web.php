<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('auth.login');
});
Route::get('/auth-user', 'Auth\AuthController@loginPage')->name('auth.page');
Route::post('/auth-user', 'Auth\AuthController@loginUser')->name('auth.submit');

Auth::routes();
Route::get('logout', function ()
{
    auth()->logout();
    Session()->flush();

    return Redirect::to('/login');
})->name('logout');
Route::get('admin', 'Admin\AdminController@index')->name('admin.showShipmentRequest');
Route::get('admin/shipment-request/history', 'Admin\AdminController@indexHistory')->name('admin.showShipmentRequestHistory');
Route::get('admin/show-resi/{id}', 'Admin\AdminController@showResi');
Route::get('admin/courier-shipment-request', 'Admin\KurirShipmentRequestController@index');
Route::get('admin/courier-shipment-request/edit/{id}', 'Admin\KurirShipmentRequestController@edit')->name('shipment-request.edit');
Route::get('admin/courier-shipment-request/delete/{id}', 'Admin\KurirShipmentRequestController@destroy')->name('shipment-request.destroy');