<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class MfaService implements MfaServiceInterface
{
    /**
     * Request OTP from external MFA service (Mock implementation)
     *
     * @param string $email User email
     * @param string $phone User phone number (optional)
     * @return array Response containing success status and message
     */
    public function requestOtp(string $email, string $phone = null): array
    {
        try {
            // Mock external API call - in real implementation, this would call actual MFA service
            Log::info("MFA OTP Request", [
                'email' => $email,
                'phone' => $phone,
                'timestamp' => now()
            ]);

            // Simulate API call delay
            usleep(500000); // 0.5 second delay

            // Generate mock OTP and store in cache for validation
            $otp = $this->generateMockOtp();
            $cacheKey = "mfa_otp_{$email}";
            
            // Store OTP in cache for 5 minutes
            Cache::put($cacheKey, $otp, 300);

            // In development, log the OTP for testing purposes
            if (config('app.debug')) {
                Log::info("Generated OTP for {$email}: {$otp}");
            }

            return [
                'success' => true,
                'message' => 'OTP sent successfully',
                'otp_for_testing' => config('app.debug') ? $otp : null // Only include in debug mode
            ];

        } catch (\Exception $e) {
            Log::error("MFA OTP Request Failed", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send OTP. Please try again.'
            ];
        }
    }

    /**
     * Validate OTP with external MFA service (Mock implementation)
     *
     * @param string $email User email
     * @param string $otp OTP code to validate
     * @return array Response containing validation result
     */
    public function validateOtp(string $email, string $otp): array
    {
        try {
            $cacheKey = "mfa_otp_{$email}";
            $storedOtp = Cache::get($cacheKey);

            // Mock external API call - in real implementation, this would call actual MFA service
            Log::info("MFA OTP Validation", [
                'email' => $email,
                'provided_otp' => $otp,
                'timestamp' => now()
            ]);

            // Simulate API call delay
            usleep(300000); // 0.3 second delay

            if (!$storedOtp) {
                return [
                    'success' => false,
                    'message' => 'OTP has expired or was not requested'
                ];
            }

            if ($storedOtp === $otp) {
                // Remove OTP from cache after successful validation
                Cache::forget($cacheKey);
                
                return [
                    'success' => true,
                    'message' => 'OTP validated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid OTP code'
                ];
            }

        } catch (\Exception $e) {
            Log::error("MFA OTP Validation Failed", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'OTP validation failed. Please try again.'
            ];
        }
    }

    /**
     * Generate session token for MFA verification
     *
     * @param string $email User email
     * @return string Session token
     */
    public function generateMfaToken(string $email): string
    {
        $token = Str::random(64);
        $cacheKey = "mfa_token_{$token}";
        
        // Store token with email for 10 minutes
        Cache::put($cacheKey, $email, 600);
        
        return $token;
    }

    /**
     * Validate MFA session token
     *
     * @param string $token MFA session token
     * @return bool True if token is valid
     */
    public function validateMfaToken(string $token): bool
    {
        $cacheKey = "mfa_token_{$token}";
        return Cache::has($cacheKey);
    }

    /**
     * Get email associated with MFA token
     *
     * @param string $token MFA session token
     * @return string|null Email if token is valid
     */
    public function getEmailFromToken(string $token): ?string
    {
        $cacheKey = "mfa_token_{$token}";
        return Cache::get($cacheKey);
    }

    /**
     * Clear MFA token after successful authentication
     *
     * @param string $token MFA session token
     * @return void
     */
    public function clearMfaToken(string $token): void
    {
        $cacheKey = "mfa_token_{$token}";
        Cache::forget($cacheKey);
    }

    /**
     * Generate mock OTP for testing
     *
     * @return string 6-digit OTP
     */
    private function generateMockOtp(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }
}
