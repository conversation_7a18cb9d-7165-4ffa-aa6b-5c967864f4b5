<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

class MfaService implements MfaServiceInterface
{
    /**
     * Debug mode configuration
     * Set to true to enable debug logging and OTP exposure for testing
     * Set to false for production to disable debug features
     */
    const DEBUG_MODE = true;

    /**
     * Use real API integration
     * Set to true to use real ESB API endpoints (requires ESB_GO_URL in .env)
     * Set to false to use mock implementation for testing
     *
     * When USE_REAL_API = true:
     * - Calls POST {ESB_GO_URL}/api/v1/esb/send-otp-email for OTP requests
     * - Calls POST {ESB_GO_URL}/api/v1/esb/submit-otp-email for OTP validation
     * - Uses transaction_id from API response for validation
     *
     * When USE_REAL_API = false:
     * - Uses mock implementation with cache-based OTP storage
     * - Generates random 6-digit OTP codes for testing
     */
    const USE_REAL_API = false;

    /**
     * Request OTP from external MFA service (Mock implementation)
     *
     * @param string $email User email
     * @param string $phone User phone number (optional)
     * @return array Response containing success status and message
     */
    public function requestOtp(string $email, string $phone = null): array
    {
        try {
            Log::info("MFA OTP Request", [
                'email' => $email,
                'phone' => $phone,
                'timestamp' => now(),
                'using_real_api' => self::USE_REAL_API
            ]);

            if (self::USE_REAL_API) {
                return $this->requestOtpFromApi($email);
            } else {
                return $this->requestOtpMock($email);
            }

        } catch (\Exception $e) {
            Log::error("MFA OTP Request Failed", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send OTP. Please try again.'
            ];
        }
    }

    /**
     * Request OTP from real ESB API
     */
    private function requestOtpFromApi(string $email): array
    {
        $esbUrl = config('app.esb_go_url', env('ESB_GO_URL'));

        if (!$esbUrl) {
            throw new \Exception('ESB_GO_URL not configured');
        }

        $response = Http::timeout(30)->post("{$esbUrl}/api/v1/esb/send-otp-email", [
            'email' => $email
        ]);

        if ($response->successful()) {
            $data = $response->json();

            // Store transaction_id in cache for verification
            if (isset($data['transaction_id'])) {
                $cacheKey = "mfa_transaction_{$email}";
                Cache::put($cacheKey, $data['transaction_id'], 300); // 5 minutes
            }

            return [
                'success' => true,
                'message' => 'OTP sent successfully',
                'transaction_id' => $data['transaction_id'] ?? null
            ];
        } else {
            Log::error("ESB API Error", [
                'email' => $email,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send OTP via API. Please try again.'
            ];
        }
    }

    /**
     * Mock OTP request for testing
     */
    private function requestOtpMock(string $email): array
    {
        // Simulate API call delay
        usleep(500000); // 0.5 second delay

        // Generate mock OTP and store in cache for validation
        $otp = $this->generateMockOtp();
        $cacheKey = "mfa_otp_{$email}";

        // Store OTP in cache for 5 minutes
        Cache::put($cacheKey, $otp, 300);

        // In debug mode, log the OTP for testing purposes
        if (self::DEBUG_MODE) {
            Log::info("Generated OTP for {$email}: {$otp}");
        }

        return [
            'success' => true,
            'message' => 'OTP sent successfully',
            'otp_for_testing' => self::DEBUG_MODE ? $otp : null // Only include in debug mode
        ];
    }

    /**
     * Validate OTP with external MFA service (Mock implementation)
     *
     * @param string $email User email
     * @param string $otp OTP code to validate
     * @return array Response containing validation result
     */
    public function validateOtp(string $email, string $otp): array
    {
        try {
            Log::info("MFA OTP Validation", [
                'email' => $email,
                'provided_otp' => $otp,
                'timestamp' => now(),
                'using_real_api' => self::USE_REAL_API
            ]);

            if (self::USE_REAL_API) {
                return $this->validateOtpWithApi($email, $otp);
            } else {
                return $this->validateOtpMock($email, $otp);
            }

        } catch (\Exception $e) {
            Log::error("MFA OTP Validation Failed", [
                'email' => $email,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'OTP validation failed. Please try again.'
            ];
        }
    }

    /**
     * Validate OTP with real ESB API
     */
    private function validateOtpWithApi(string $email, string $otp): array
    {
        $esbUrl = config('app.esb_go_url', env('ESB_GO_URL'));

        if (!$esbUrl) {
            throw new \Exception('ESB_GO_URL not configured');
        }

        // Get transaction_id from cache
        $transactionCacheKey = "mfa_transaction_{$email}";
        $transactionId = Cache::get($transactionCacheKey);

        if (!$transactionId) {
            return [
                'success' => false,
                'message' => 'OTP session has expired. Please request a new OTP.'
            ];
        }

        $response = Http::timeout(30)->post("{$esbUrl}/api/v1/esb/submit-otp-email", [
            'email' => $email,
            'token' => $otp,
            'transaction_id' => $transactionId
        ]);

        if ($response->successful()) {
            $data = $response->json();

            // Clear transaction from cache after successful validation
            Cache::forget($transactionCacheKey);

            return [
                'success' => true,
                'message' => 'OTP validated successfully'
            ];
        } else {
            Log::error("ESB API Validation Error", [
                'email' => $email,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'message' => 'Invalid OTP code or validation failed.'
            ];
        }
    }

    /**
     * Mock OTP validation for testing
     */
    private function validateOtpMock(string $email, string $otp): array
    {
        $cacheKey = "mfa_otp_{$email}";
        $storedOtp = Cache::get($cacheKey);

        // Simulate API call delay
        usleep(300000); // 0.3 second delay

        if (!$storedOtp) {
            return [
                'success' => false,
                'message' => 'OTP has expired or was not requested'
            ];
        }

        if ($storedOtp === $otp) {
            // Remove OTP from cache after successful validation
            Cache::forget($cacheKey);

            return [
                'success' => true,
                'message' => 'OTP validated successfully'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Invalid OTP code'
            ];
        }
    }

    /**
     * Generate session token for MFA verification
     *
     * @param string $email User email
     * @return string Session token
     */
    public function generateMfaToken(string $email): string
    {
        $token = Str::random(64);
        $cacheKey = "mfa_token_{$token}";
        
        // Store token with email for 10 minutes
        Cache::put($cacheKey, $email, 600);
        
        return $token;
    }

    /**
     * Validate MFA session token
     *
     * @param string $token MFA session token
     * @return bool True if token is valid
     */
    public function validateMfaToken(string $token): bool
    {
        $cacheKey = "mfa_token_{$token}";
        return Cache::has($cacheKey);
    }

    /**
     * Get email associated with MFA token
     *
     * @param string $token MFA session token
     * @return string|null Email if token is valid
     */
    public function getEmailFromToken(string $token): ?string
    {
        $cacheKey = "mfa_token_{$token}";
        return Cache::get($cacheKey);
    }

    /**
     * Clear MFA token after successful authentication
     *
     * @param string $token MFA session token
     * @return void
     */
    public function clearMfaToken(string $token): void
    {
        $cacheKey = "mfa_token_{$token}";
        Cache::forget($cacheKey);
    }

    /**
     * Generate mock OTP for testing
     *
     * @return string 6-digit OTP
     */
    private function generateMockOtp(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }
}
