﻿<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="">
        <meta name="author" content="">
        <title>Buttons - SB Admin Pro</title>
        <link href="css/styles.css" rel="stylesheet">
        <link rel="icon" type="image/x-icon" href="assets/img/favicon.png">
        <script data-search-pseudo-elements="" defer="" src="ajax/libs/font-awesome/6.3.0/js/all.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/feather-icons/4.29.0/feather.min.js" crossorigin="anonymous"></script>
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i data-feather="menu"></i></button>
            <!-- Navbar Brand-->
            <!-- * * Tip * * You can use text or an image for your navbar brand.-->
            <!-- * * * * * * When using an image, we recommend the SVG format.-->
            <!-- * * * * * * Dimensions: Maximum height: 32px, maximum width: 240px-->
            <a class="navbar-brand pe-3 ps-4 ps-lg-2" href="index.html">SB Admin Pro</a>
            <!-- Navbar Search Input-->
            <!-- * * Note: * * Visible only on and above the lg breakpoint-->
            <form class="form-inline me-auto d-none d-lg-block me-3">
                <div class="input-group input-group-joined input-group-solid">
                    <input class="form-control pe-0" type="search" placeholder="Search" aria-label="Search">
                    <div class="input-group-text"><i data-feather="search"></i></div>
                </div>
            </form>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto">
                <!-- Documentation Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-md-block me-3">
                    <a class="nav-link dropdown-toggle" id="navbarDropdownDocs" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <div class="fw-500">Documentation</div>
                        <i class="fas fa-chevron-right dropdown-arrow"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end py-0 me-sm-n15 me-lg-0 o-hidden animated--fade-in-up" aria-labelledby="navbarDropdownDocs">
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="book"></i></div>
                            <div>
                                <div class="small text-gray-500">Documentation</div>
                                Usage instructions and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/components" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="code"></i></div>
                            <div>
                                <div class="small text-gray-500">Components</div>
                                Code snippets and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/changelog" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="file-text"></i></div>
                            <div>
                                <div class="small text-gray-500">Changelog</div>
                                Updates and changes
                            </div>
                        </a>
                    </div>
                </li>
                <!-- Navbar Search Dropdown-->
                <!-- * * Note: * * Visible only below the lg breakpoint-->
                <li class="nav-item dropdown no-caret me-3 d-lg-none">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="searchDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="search"></i></a>
                    <!-- Dropdown - Search-->
                    <div class="dropdown-menu dropdown-menu-end p-3 shadow animated--fade-in-up" aria-labelledby="searchDropdown">
                        <form class="form-inline me-auto w-100">
                            <div class="input-group input-group-joined input-group-solid">
                                <input class="form-control pe-0" type="text" placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2">
                                <div class="input-group-text"><i data-feather="search"></i></div>
                            </div>
                        </form>
                    </div>
                </li>
                <!-- Alerts Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownAlerts" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="bell"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownAlerts">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="bell"></i>
                            Alerts Center
                        </h6>
                        <!-- Example Alert 1-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-warning"><i data-feather="activity"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 29, 2021</div>
                                <div class="dropdown-notifications-item-content-text">This is an alert message. It's nothing serious, but it requires your attention.</div>
                            </div>
                        </a>
                        <!-- Example Alert 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-info"><i data-feather="bar-chart"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 22, 2021</div>
                                <div class="dropdown-notifications-item-content-text">A new monthly report is ready. Click here to view!</div>
                            </div>
                        </a>
                        <!-- Example Alert 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-danger"><i class="fas fa-exclamation-triangle"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 8, 2021</div>
                                <div class="dropdown-notifications-item-content-text">Critical system failure, systems shutting down.</div>
                            </div>
                        </a>
                        <!-- Example Alert 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-success"><i data-feather="user-plus"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 2, 2021</div>
                                <div class="dropdown-notifications-item-content-text">New user request. Woody has requested access to the organization.</div>
                            </div>
                        </a>
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">View All Alerts</a>
                    </div>
                </li>
                <!-- Messages Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownMessages" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="mail"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownMessages">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="mail"></i>
                            Message Center
                        </h6>
                        <!-- Example Message 1  -->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-2.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Thomas Wilcox · 58m</div>
                            </div>
                        </a>
                        <!-- Example Message 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-3.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Emily Fowler · 2d</div>
                            </div>
                        </a>
                        <!-- Example Message 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-4.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Marshall Rosencrantz · 3d</div>
                            </div>
                        </a>
                        <!-- Example Message 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-5.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Colby Newton · 3d</div>
                            </div>
                        </a>
                        <!-- Footer Link-->
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">Read All Messages</a>
                    </div>
                </li>
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="assets/img/illustrations/profiles/profile-1.png"></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="assets/img/illustrations/profiles/profile-1.png">
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">Valerie Luna</div>
                                <div class="dropdown-user-details-email"><a href="cdn-cgi/l/email-protection.html" class="__cf_email__" data-cfemail="354359405b5475545a591b565a58">[email&#160;protected]</a></div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <!-- Sidenav Menu Heading (Account)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <div class="sidenav-menu-heading d-sm-none">Account</div>
                            <!-- Sidenav Link (Alerts)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="bell"></i></div>
                                Alerts
                                <span class="badge bg-warning-soft text-warning ms-auto">4 New!</span>
                            </a>
                            <!-- Sidenav Link (Messages)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="mail"></i></div>
                                Messages
                                <span class="badge bg-success-soft text-success ms-auto">2 New!</span>
                            </a>
                            <!-- Sidenav Menu Heading (Core)-->
                            <div class="sidenav-menu-heading">Core</div>
                            <!-- Sidenav Accordion (Dashboard)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseDashboards" aria-expanded="false" aria-controls="collapseDashboards">
                                <div class="nav-link-icon"><i data-feather="activity"></i></div>
                                Dashboards
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseDashboards" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPages">
                                    <a class="nav-link" href="dashboard-1.html">
                                        Default
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dashboard-2.html">Multipurpose</a>
                                    <a class="nav-link" href="dashboard-3.html">Affiliate</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Custom)-->
                            <div class="sidenav-menu-heading">Custom</div>
                            <!-- Sidenav Accordion (Pages)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapsePages" aria-expanded="false" aria-controls="collapsePages">
                                <div class="nav-link-icon"><i data-feather="grid"></i></div>
                                Pages
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapsePages" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesMenu">
                                    <!-- Nested Sidenav Accordion (Pages -> Account)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAccount" aria-expanded="false" aria-controls="pagesCollapseAccount">
                                        Account
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAccount" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="account-profile.html">Profile</a>
                                            <a class="nav-link" href="account-billing.html">Billing</a>
                                            <a class="nav-link" href="account-security.html">Security</a>
                                            <a class="nav-link" href="account-notifications.html">Notifications</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Authentication)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuth" aria-expanded="false" aria-controls="pagesCollapseAuth">
                                        Authentication
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAuth" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesAuth">
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Basic)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthBasic" aria-expanded="false" aria-controls="pagesCollapseAuthBasic">
                                                Basic
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthBasic" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-basic.html">Login</a>
                                                    <a class="nav-link" href="auth-register-basic.html">Register</a>
                                                    <a class="nav-link" href="auth-password-basic.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Social)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthSocial" aria-expanded="false" aria-controls="pagesCollapseAuthSocial">
                                                Social
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthSocial" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-social.html">Login</a>
                                                    <a class="nav-link" href="auth-register-social.html">Register</a>
                                                    <a class="nav-link" href="auth-password-social.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Error)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseError" aria-expanded="false" aria-controls="pagesCollapseError">
                                        Error
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseError" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="error-400.html">400 Error</a>
                                            <a class="nav-link" href="error-401.html">401 Error</a>
                                            <a class="nav-link" href="error-403.html">403 Error</a>
                                            <a class="nav-link" href="error-404-1.html">404 Error 1</a>
                                            <a class="nav-link" href="error-404-2.html">404 Error 2</a>
                                            <a class="nav-link" href="error-500.html">500 Error</a>
                                            <a class="nav-link" href="error-503.html">503 Error</a>
                                            <a class="nav-link" href="error-504.html">504 Error</a>
                                        </nav>
                                    </div>
                                    <a class="nav-link" href="pricing.html">Pricing</a>
                                    <a class="nav-link" href="invoice.html">Invoice</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Applications)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseApps" aria-expanded="false" aria-controls="collapseApps">
                                <div class="nav-link-icon"><i data-feather="globe"></i></div>
                                Applications
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseApps" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavAppsMenu">
                                    <!-- Nested Sidenav Accordion (Apps -> Knowledge Base)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseKnowledgeBase" aria-expanded="false" aria-controls="appsCollapseKnowledgeBase">
                                        Knowledge Base
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseKnowledgeBase" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="knowledge-base-home-1.html">Home 1</a>
                                            <a class="nav-link" href="knowledge-base-home-2.html">Home 2</a>
                                            <a class="nav-link" href="knowledge-base-category.html">Category</a>
                                            <a class="nav-link" href="knowledge-base-article.html">Article</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> User Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseUserManagement" aria-expanded="false" aria-controls="appsCollapseUserManagement">
                                        User Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseUserManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="user-management-list.html">Users List</a>
                                            <a class="nav-link" href="user-management-edit-user.html">Edit User</a>
                                            <a class="nav-link" href="user-management-add-user.html">Add User</a>
                                            <a class="nav-link" href="user-management-groups-list.html">Groups List</a>
                                            <a class="nav-link" href="user-management-org-details.html">Organization Details</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> Posts Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapsePostsManagement" aria-expanded="false" aria-controls="appsCollapsePostsManagement">
                                        Posts Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapsePostsManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="blog-management-posts-list.html">Posts List</a>
                                            <a class="nav-link" href="blog-management-create-post.html">Create Post</a>
                                            <a class="nav-link" href="blog-management-edit-post.html">Edit Post</a>
                                            <a class="nav-link" href="blog-management-posts-admin.html">Posts Admin</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Flows)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseFlows" aria-expanded="false" aria-controls="collapseFlows">
                                <div class="nav-link-icon"><i data-feather="repeat"></i></div>
                                Flows
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseFlows" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="multi-tenant-select.html">Multi-Tenant Registration</a>
                                    <a class="nav-link" href="wizard.html">Wizard</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (UI Toolkit)-->
                            <div class="sidenav-menu-heading">UI Toolkit</div>
                            <!-- Sidenav Accordion (Layout)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayouts" aria-expanded="false" aria-controls="collapseLayouts">
                                <div class="nav-link-icon"><i data-feather="layout"></i></div>
                                Layout
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseLayouts" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavLayout">
                                    <!-- Nested Sidenav Accordion (Layout -> Navigation)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutSidenavVariations" aria-expanded="false" aria-controls="collapseLayoutSidenavVariations">
                                        Navigation
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutSidenavVariations" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-static.html">Static Sidenav</a>
                                            <a class="nav-link" href="layout-dark.html">Dark Sidenav</a>
                                            <a class="nav-link" href="layout-rtl.html">RTL Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Container Options)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutContainers" aria-expanded="false" aria-controls="collapseLayoutContainers">
                                        Container Options
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutContainers" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-boxed.html">Boxed Layout</a>
                                            <a class="nav-link" href="layout-fluid.html">Fluid Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Page Headers)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsPageHeaders" aria-expanded="false" aria-controls="collapseLayoutsPageHeaders">
                                        Page Headers
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsPageHeaders" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="header-simplified.html">Simplified</a>
                                            <a class="nav-link" href="header-compact.html">Compact</a>
                                            <a class="nav-link" href="header-overlap.html">Content Overlap</a>
                                            <a class="nav-link" href="header-breadcrumbs.html">Breadcrumbs</a>
                                            <a class="nav-link" href="header-light.html">Light</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Starter Layouts)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsStarterTemplates" aria-expanded="false" aria-controls="collapseLayoutsStarterTemplates">
                                        Starter Layouts
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsStarterTemplates" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="starter-default.html">Default</a>
                                            <a class="nav-link" href="starter-minimal.html">Minimal</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Components)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseComponents" aria-expanded="false" aria-controls="collapseComponents">
                                <div class="nav-link-icon"><i data-feather="package"></i></div>
                                Components
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseComponents" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="alerts.html">Alerts</a>
                                    <a class="nav-link" href="avatars.html">Avatars</a>
                                    <a class="nav-link" href="badges.html">Badges</a>
                                    <a class="nav-link" href="buttons.html">Buttons</a>
                                    <a class="nav-link" href="cards.html">
                                        Cards
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dropdowns.html">Dropdowns</a>
                                    <a class="nav-link" href="forms.html">
                                        Forms
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="modals.html">Modals</a>
                                    <a class="nav-link" href="navigation.html">Navigation</a>
                                    <a class="nav-link" href="progress.html">Progress</a>
                                    <a class="nav-link" href="step.html">Step</a>
                                    <a class="nav-link" href="timeline.html">Timeline</a>
                                    <a class="nav-link" href="toasts.html">Toasts</a>
                                    <a class="nav-link" href="tooltips.html">Tooltips</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Utilities)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseUtilities" aria-expanded="false" aria-controls="collapseUtilities">
                                <div class="nav-link-icon"><i data-feather="tool"></i></div>
                                Utilities
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseUtilities" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="animations.html">Animations</a>
                                    <a class="nav-link" href="background.html">Background</a>
                                    <a class="nav-link" href="borders.html">Borders</a>
                                    <a class="nav-link" href="lift.html">Lift</a>
                                    <a class="nav-link" href="shadows.html">Shadows</a>
                                    <a class="nav-link" href="typography.html">Typography</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Addons)-->
                            <div class="sidenav-menu-heading">Plugins</div>
                            <!-- Sidenav Link (Charts)-->
                            <a class="nav-link" href="charts.html">
                                <div class="nav-link-icon"><i data-feather="bar-chart"></i></div>
                                Charts
                            </a>
                            <!-- Sidenav Link (Tables)-->
                            <a class="nav-link" href="tables.html">
                                <div class="nav-link-icon"><i data-feather="filter"></i></div>
                                Tables
                            </a>
                        </div>
                    </div>
                    <!-- Sidenav Footer-->
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title">Valerie Luna</div>
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-dark bg-gradient-primary-to-secondary pb-10">
                        <div class="container-xl px-4">
                            <div class="page-header-content pt-4">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mt-4">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i data-feather="mouse-pointer"></i></div>
                                            Buttons
                                        </h1>
                                        <div class="page-header-subtitle">Restyled Bootstrap default buttons and custom developed button components</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <!-- Main page content-->
                    <div class="container-xl px-4 mt-n10">
                        <div class="row">
                            <!-- Style Reference-->
                            <div class="col-lg-9">
                                <!-- Default Bootstrap Buttons-->
                                <div id="default">
                                    <div class="card mb-4">
                                        <div class="card-header">Default Bootstrap Buttons</div>
                                        <div class="card-body">
                                            <!-- Default Bootstrap Buttons Component Preview-->
                                            <h6 class="small text-muted fw-500" id="defaultSolid">Bootstrap Buttons:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-primary me-2 my-1" type="button">Primary</button>
                                                    <button class="btn btn-secondary me-2 my-1" type="button">Secondary</button>
                                                    <button class="btn btn-success me-2 my-1" type="button">Success</button>
                                                    <button class="btn btn-danger me-2 my-1" type="button">Danger</button>
                                                    <button class="btn btn-warning me-2 my-1" type="button">Warning</button>
                                                    <button class="btn btn-info me-2 my-1" type="button">Info</button>
                                                    <button class="btn btn-light me-2 my-1" type="button">Light</button>
                                                    <button class="btn btn-dark me-2 my-1" type="button">Dark</button>
                                                    <button class="btn btn-link my-1" type="button">Link</button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsDefaultTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsDefaultHtmlTab" data-bs-toggle="tab" href="#buttonsDefaultHtml" role="tab" aria-controls="buttonsDefaultHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsDefaultPugTab" data-bs-toggle="tab" href="#buttonsDefaultPug" role="tab" aria-controls="buttonsDefaultPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsDefaultHtml" role="tabpanel" aria-labelledby="buttonsDefaultHtmlTab">
                                                            <pre class="language-markup"><code><script data-cfasync="false" src="cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script type="text/plain"><button class="btn btn-primary" type="button">Primary</button>
<button class="btn btn-secondary" type="button">Secondary</button>
<button class="btn btn-success" type="button">Success</button>
<button class="btn btn-danger" type="button">Danger</button>
<button class="btn btn-warning" type="button">Warning</button>
<button class="btn btn-info" type="button">Info</button>
<button class="btn btn-light" type="button">Light</button>
<button class="btn btn-dark" type="button">Dark</button>
<button class="btn btn-link" type="button">Link</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsDefaultPug" role="tabpanel" aria-labelledby="buttonsDefaultPugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-primary(type='button') Primary
button.btn.btn-secondary(type='button') Secondary
button.btn.btn-success(type='button') Success
button.btn.btn-danger(type='button') Danger
button.btn.btn-warning(type='button') Warning
button.btn.btn-info(type='button') Info
button.btn.btn-light(type='button') Light
button.btn.btn-dark(type='button') Dark
button.btn.btn-link(type='button') Link</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Bootstrap's default state-specific button styles have been styled to fit the SB Admin Pro theme.</div>
                                            </div>
                                            <!-- Default Bootstrap Outline Buttons Component Preview-->
                                            <h6 class="small text-muted fw-500" id="defaultOutline">Bootstrap Outline Buttons:</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-outline-primary me-2 my-1" type="button">Primary</button>
                                                    <button class="btn btn-outline-secondary me-2 my-1" type="button">Secondary</button>
                                                    <button class="btn btn-outline-success me-2 my-1" type="button">Success</button>
                                                    <button class="btn btn-outline-danger me-2 my-1" type="button">Danger</button>
                                                    <button class="btn btn-outline-warning me-2 my-1" type="button">Warning</button>
                                                    <button class="btn btn-outline-info me-2 my-1" type="button">Info</button>
                                                    <button class="btn btn-outline-light me-2 my-1" type="button">Light</button>
                                                    <button class="btn btn-outline-dark my-1" type="button">Dark</button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsDefaultOutlineTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsDefaultOutlineHtmlTab" data-bs-toggle="tab" href="#buttonsDefaultOutlineHtml" role="tab" aria-controls="buttonsDefaultOutlineHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsDefaultOutlinePugTab" data-bs-toggle="tab" href="#buttonsDefaultOutlinePug" role="tab" aria-controls="buttonsDefaultOutlinePug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsDefaultOutlineHtml" role="tabpanel" aria-labelledby="buttonsDefaultOutlineHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-outline-primary" type="button">Primary</button>
<button class="btn btn-outline-secondary" type="button">Secondary</button>
<button class="btn btn-outline-success" type="button">Success</button>
<button class="btn btn-outline-danger" type="button">Danger</button>
<button class="btn btn-outline-warning" type="button">Warning</button>
<button class="btn btn-outline-info" type="button">Info</button>
<button class="btn btn-outline-light" type="button">Light</button>
<button class="btn btn-outline-dark" type="button">Dark</button>
<button class="btn btn-outline-link" type="button">Link</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsDefaultOutlinePug" role="tabpanel" aria-labelledby="buttonsDefaultOutlinePugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-outline-primary(type='button') Primary
button.btn.btn-outline-secondary(type='button') Secondary
button.btn.btn-outline-success(type='button') Success
button.btn.btn-outline-danger(type='button') Danger
button.btn.btn-outline-warning(type='button') Warning
button.btn.btn-outline-info(type='button') Info
button.btn.btn-outline-light(type='button') Light
button.btn.btn-outline-dark(type='button') Dark
button.btn.btn-outline-link(type='button') Link</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Outline button styles are also available by default from Bootstrap.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Custom Button Colors-->
                                <div id="customColors">
                                    <div class="card mb-4">
                                        <div class="card-header">Custom Button Colors</div>
                                        <div class="card-body">
                                            <!-- Custom Button Colors Component Preview-->
                                            <h6 class="small text-muted fw-500" id="customColorsSolid">Non-Contextual Button Colors:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-red me-2 my-1" type="button">Red</button>
                                                    <button class="btn btn-orange me-2 my-1" type="button">Orange</button>
                                                    <button class="btn btn-yellow me-2 my-1" type="button">Yellow</button>
                                                    <button class="btn btn-green me-2 my-1" type="button">Green</button>
                                                    <button class="btn btn-teal me-2 my-1" type="button">Teal</button>
                                                    <button class="btn btn-cyan me-2 my-1" type="button">Cyan</button>
                                                    <button class="btn btn-blue me-2 my-1" type="button">Blue</button>
                                                    <button class="btn btn-indigo me-2 my-1" type="button">Indigo</button>
                                                    <button class="btn btn-purple me-2 my-1" type="button">Purple</button>
                                                    <button class="btn btn-pink my-1" type="button">Pink</button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsCustomTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsCustomHtmlTab" data-bs-toggle="tab" href="#buttonsCustomHtml" role="tab" aria-controls="buttonsCustomHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsCustomPugTab" data-bs-toggle="tab" href="#buttonsCustomPug" role="tab" aria-controls="buttonsCustomPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsCustomHtml" role="tabpanel" aria-labelledby="buttonsCustomHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-red" type="button">Red</button>
<button class="btn btn-orange" type="button">Orange</button>
<button class="btn btn-yellow" type="button">Yellow</button>
<button class="btn btn-green" type="button">Green</button>
<button class="btn btn-teal" type="button">Teal</button>
<button class="btn btn-cyan" type="button">Cyan</button>
<button class="btn btn-blue" type="button">Blue</button>
<button class="btn btn-indigo" type="button">Indigo</button>
<button class="btn btn-purple" type="button">Purple</button>
<button class="btn btn-pink" type="button">Pink</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsCustomPug" role="tabpanel" aria-labelledby="buttonsCustomPugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-red(type='button') Red
button.btn.btn-orange(type='button') Orange
button.btn.btn-yellow(type='button') Yellow
button.btn.btn-green(type='button') Green
button.btn.btn-teal(type='button') Teal
button.btn.btn-cyan(type='button') Cyan
button.btn.btn-blue(type='button') Blue
button.btn.btn-indigo(type='button') Indigo
button.btn.btn-purple(type='button') Purple
button.btn.btn-pink(type='button') Pink</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">By default, Bootstrap's buttons are semantically named. We've extended the Bootstrap framework to include non-contextual colors.</div>
                                            </div>
                                            <!-- Custom Button Outline Colors Component Preview-->
                                            <h6 class="small text-muted fw-500" id="customColorsOutline">Non-Contextual Outline Button Colors:</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-outline-red me-2 my-1" type="button">Red</button>
                                                    <button class="btn btn-outline-orange me-2 my-1" type="button">Orange</button>
                                                    <button class="btn btn-outline-yellow me-2 my-1" type="button">Yellow</button>
                                                    <button class="btn btn-outline-green me-2 my-1" type="button">Green</button>
                                                    <button class="btn btn-outline-teal me-2 my-1" type="button">Teal</button>
                                                    <button class="btn btn-outline-cyan me-2 my-1" type="button">Cyan</button>
                                                    <button class="btn btn-outline-blue me-2 my-1" type="button">Blue</button>
                                                    <button class="btn btn-outline-indigo me-2 my-1" type="button">Indigo</button>
                                                    <button class="btn btn-outline-purple me-2 my-1" type="button">Purple</button>
                                                    <button class="btn btn-outline-pink my-1" type="button">Pink</button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsCustomOutlineTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsCustomOutlineHtmlTab" data-bs-toggle="tab" href="#buttonsCustomOutlineHtml" role="tab" aria-controls="buttonsCustomOutlineHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsCustomOutlinePugTab" data-bs-toggle="tab" href="#buttonsCustomOutlinePug" role="tab" aria-controls="buttonsCustomOutlinePug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsCustomOutlineHtml" role="tabpanel" aria-labelledby="buttonsCustomOutlineHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-outline-red" type="button">Red</button>
<button class="btn btn-outline-orange" type="button">Orange</button>
<button class="btn btn-outline-yellow" type="button">Yellow</button>
<button class="btn btn-outline-green" type="button">Green</button>
<button class="btn btn-outline-teal" type="button">Teal</button>
<button class="btn btn-outline-cyan" type="button">Cyan</button>
<button class="btn btn-outline-blue" type="button">Blue</button>
<button class="btn btn-outline-indigo" type="button">Indigo</button>
<button class="btn btn-outline-purple" type="button">Purple</button>
<button class="btn btn-outline-pink" type="button">Pink</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsCustomOutlinePug" role="tabpanel" aria-labelledby="buttonsCustomOutlinePugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-outline-red(type='button') Red
button.btn.btn-outline-orange(type='button') Orange
button.btn.btn-outline-yellow(type='button') Yellow
button.btn.btn-outline-green(type='button') Green
button.btn.btn-outline-teal(type='button') Teal
button.btn.btn-outline-cyan(type='button') Cyan
button.btn.btn-outline-blue(type='button') Blue
button.btn.btn-outline-indigo(type='button') Indigo
button.btn.btn-outline-purple(type='button') Purple
button.btn.btn-outline-pink(type='button') Pink</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">SB Admin Pro's non-contextual color classes work with outline buttons as well!</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Custom Icon Buttons-->
                                <div id="icon">
                                    <div class="card mb-4">
                                        <div class="card-header">Custom Icon Buttons</div>
                                        <div class="card-body">
                                            <!-- Icon Buttons (Feather Icons) Component Preview-->
                                            <h6 class="small text-muted fw-500" id="iconFeather">Icon Buttons (Feather Icons):</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-red btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-orange btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-yellow btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-green btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-teal btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-cyan btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-blue btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-indigo btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-purple btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-pink btn-icon my-1" type="button"><i data-feather="feather"></i></button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsIconFeatherTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsIconFeatherHtmlTab" data-bs-toggle="tab" href="#buttonsIconFeatherHtml" role="tab" aria-controls="buttonsIconFeatherHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsIconFeatherPugTab" data-bs-toggle="tab" href="#buttonsIconFeatherPug" role="tab" aria-controls="buttonsIconFeatherPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsIconFeatherHtml" role="tabpanel" aria-labelledby="buttonsIconFeatherHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-red btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-orange btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-yellow btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-green btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-teal btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-cyan btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-blue btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-indigo btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-purple btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-pink btn-icon" type="button">
    <i data-feather="feather"></i>
</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsIconFeatherPug" role="tabpanel" aria-labelledby="buttonsIconFeatherPugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-red.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-orange.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-yellow.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-green.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-teal.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-cyan.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-blue.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-indigo.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-purple.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-pink.btn-icon(type='button')
    i(data-feather='feather')</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Our custom icon button component is circular by default, and looks great when used with Feather Icons, which are included with this theme.</div>
                                            </div>
                                            <!-- Icon Buttons (Font Awesome Icons) Component Preview-->
                                            <h6 class="small text-muted fw-500" id="iconFontAwesome">Icon Buttons (Font Awesome Icons):</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-red btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-orange btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-yellow btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-green btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-teal btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-cyan btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-blue btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-indigo btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-purple btn-icon me-2 my-1" type="button"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-pink btn-icon my-1" type="button"><i class="fas fa-flag"></i></button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsIconFontAwesomeTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsIconFontAwesomeHtmlTab" data-bs-toggle="tab" href="#buttonsIconFontAwesomeHtml" role="tab" aria-controls="buttonsIconFontAwesomeHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsIconFontAwesomePugTab" data-bs-toggle="tab" href="#buttonsIconFontAwesomePug" role="tab" aria-controls="buttonsIconFontAwesomePug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsIconFontAwesomeHtml" role="tabpanel" aria-labelledby="buttonsIconFontAwesomeHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-red btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-orange btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-yellow btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-green btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-teal btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-cyan btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-blue btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-indigo btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-purple btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-pink btn-icon" type="button">
    <i class="fas fa-flag"></i>
</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsIconFontAwesomePug" role="tabpanel" aria-labelledby="buttonsIconFontAwesomePugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-red.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-orange.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-yellow.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-green.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-teal.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-cyan.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-blue.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-indigo.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-purple.btn-icon(type='button')
    i.fas.fa-flag
button.btn.btn-pink.btn-icon(type='button')
    i.fas.fa-flag</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Use our icon buttons with Font Awesome as well!</div>
                                            </div>
                                            <!-- Icon Buttons (Text) Component Preview-->
                                            <h6 class="small text-muted fw-500" id="iconText">Icon Buttons (Text):</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-red btn-icon me-2 my-1" type="button">S</button>
                                                    <button class="btn btn-orange btn-icon me-2 my-1" type="button">B</button>
                                                    <button class="btn btn-yellow btn-icon me-2 my-1" type="button">A</button>
                                                    <button class="btn btn-green btn-icon me-2 my-1" type="button">D</button>
                                                    <button class="btn btn-teal btn-icon me-2 my-1" type="button">M</button>
                                                    <button class="btn btn-cyan btn-icon me-2 my-1" type="button">I</button>
                                                    <button class="btn btn-blue btn-icon me-2 my-1" type="button">N</button>
                                                    <button class="btn btn-indigo btn-icon me-2 my-1" type="button">P</button>
                                                    <button class="btn btn-purple btn-icon me-2 my-1" type="button">R</button>
                                                    <button class="btn btn-pink btn-icon my-1" type="button">O</button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsIconTextTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsIconTextHtmlTab" data-bs-toggle="tab" href="#buttonsIconTextHtml" role="tab" aria-controls="buttonsIconTextHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsIconTextPugTab" data-bs-toggle="tab" href="#buttonsIconTextPug" role="tab" aria-controls="buttonsIconTextPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsIconTextHtml" role="tabpanel" aria-labelledby="buttonsIconTextHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-red btn-icon" type="button">S </button>
<button class="btn btn-orange btn-icon" type="button">B</button>
<button class="btn btn-yellow btn-icon" type="button">A</button>
<button class="btn btn-green btn-icon" type="button">D</button>
<button class="btn btn-teal btn-icon" type="button">M</button>
<button class="btn btn-cyan btn-icon" type="button">I</button>
<button class="btn btn-blue btn-icon" type="button">N</button>
<button class="btn btn-indigo btn-icon" type="button">P</button>
<button class="btn btn-purple btn-icon" type="button">R</button>
<button class="btn btn-pink btn-icon" type="button">O</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsIconTextPug" role="tabpanel" aria-labelledby="buttonsIconTextPugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-red.btn-icon(type='button') S
button.btn.btn-orange.btn-icon(type='button') B
button.btn.btn-yellow.btn-icon(type='button') A
button.btn.btn-green.btn-icon(type='button') D
button.btn.btn-teal.btn-icon(type='button') M
button.btn.btn-cyan.btn-icon(type='button') I
button.btn.btn-blue.btn-icon(type='button') N
button.btn.btn-indigo.btn-icon(type='button') P
button.btn.btn-purple.btn-icon(type='button') R
button.btn.btn-pink.btn-icon(type='button') O</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Icon buttons use a fixed height and width, so you can use them with short strings of text, one or two characters long.</div>
                                            </div>
                                            <!-- Icon Buttons (Outline) Component Preview-->
                                            <h6 class="small text-muted fw-500" id="iconOutline">Icon Buttons (Outline):</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-outline-red btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-orange btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-yellow btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-green btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-teal btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-cyan btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-blue btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-indigo btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-purple btn-icon me-2 my-1" type="button"><i data-feather="feather"></i></button>
                                                    <button class="btn btn-outline-pink btn-icon my-1" type="button"><i data-feather="feather"></i></button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsIconOutlineTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsIconOutlineHtmlTab" data-bs-toggle="tab" href="#buttonsIconOutlineHtml" role="tab" aria-controls="buttonsIconOutlineHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsIconOutlinePugTab" data-bs-toggle="tab" href="#buttonsIconOutlinePug" role="tab" aria-controls="buttonsIconOutlinePug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsIconOutlineHtml" role="tabpanel" aria-labelledby="buttonsIconOutlineHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-outline-red btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-orange btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-yellow btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-green btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-teal btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-cyan btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-blue btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-indigo btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-purple btn-icon" type="button">
    <i data-feather="feather"></i>
</button>
<button class="btn btn-outline-pink btn-icon" type="button">
    <i data-feather="feather"></i>
</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsIconOutlinePug" role="tabpanel" aria-labelledby="buttonsIconOutlinePugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-outline-red.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-orange.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-yellow.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-green.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-teal.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-cyan.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-blue.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-indigo.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-purple.btn-icon(type='button')
    i(data-feather='feather')
button.btn.btn-outline-pink.btn-icon(type='button')
    i(data-feather='feather')</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Our custom icon button component is circular by default, and looks great when used with Feather Icons, which are included with this theme.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Custom Transparent Buttons-->
                                <div id="transparent">
                                    <div class="card mb-4">
                                        <div class="card-header">Custom Transparent Buttons</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content p-0">
                                                    <div class="bg-dark p-4 border-bottom">
                                                        <button class="btn btn-transparent-light me-2" type="button">Hover Me!</button>
                                                        <button class="btn btn-transparent-light btn-icon" type="button"><i data-feather="feather"></i></button>
                                                    </div>
                                                    <div class="p-4 border-bottom">
                                                        <button class="btn btn-transparent-dark me-2" type="button">Hover Me!</button>
                                                        <button class="btn btn-transparent-dark btn-icon" type="button"><i data-feather="feather"></i></button>
                                                    </div>
                                                    <div class="bg-dark p-4 border-bottom">
                                                        <button class="btn btn-white-10 me-2" type="button">Hover Me!</button>
                                                        <button class="btn btn-white-10 btn-icon" type="button"><i data-feather="feather"></i></button>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsTransparentTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsTransparentHtmlTab" data-bs-toggle="tab" href="#buttonsTransparentHtml" role="tab" aria-controls="buttonsTransparentHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsTransparentPugTab" data-bs-toggle="tab" href="#buttonsTransparentPug" role="tab" aria-controls="buttonsTransparentPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsTransparentHtml" role="tabpanel" aria-labelledby="buttonsTransparentHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Button Transparent (Light) -->
<button class="btn btn-transparent-light" type="button">...</button>

<!-- Button Transparent (Dark) -->
<button class="btn btn-transparent-dark" type="button">...</button>

<!-- Button Transparent (10% White Opacity) -->
<button class="btn btn-white-10" type="button">...</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsTransparentPug" role="tabpanel" aria-labelledby="buttonsTransparentPugTab">
                                                            <pre class="language-pug"><code>// Button Transparent (Light)
button.btn.btn-transparent-light(type='button') ...

// Button Transparent (Dark)
button.btn.btn-transparent-dark(type='button') ...

// Button Transparent (10% White Opacity)
button.btn.btn-white-10(type='button') ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Our transparent button component is available to use in place of a button color. This color styling works with all of the other customization options available, including icon buttons.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Button Sizing-->
                                <div id="sizing">
                                    <div class="card mb-4">
                                        <div class="card-header">Button Sizing</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500">Sizing Options:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-primary btn-xs me-2 my-1">Extra Small</button>
                                                    <button class="btn btn-primary btn-sm me-2 my-1">Small</button>
                                                    <button class="btn btn-primary me-2 my-1">Default</button>
                                                    <button class="btn btn-primary btn-lg me-2 my-1">Large</button>
                                                    <button class="btn btn-primary btn-xl my-1">Extra Large</button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonSizingTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonSizingHtmlTab" data-bs-toggle="tab" href="#buttonSizingHtml" role="tab" aria-controls="buttonSizingHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonSizingPugTab" data-bs-toggle="tab" href="#buttonSizingPug" role="tab" aria-controls="buttonSizingPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonSizingHtml" role="tabpanel" aria-labelledby="buttonSizingHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-primary btn-xs">Extra Small</button>
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary">Default</button>
<button class="btn btn-primary btn-lg">Large</button>
<button class="btn btn-primary btn-xl">Extra Large</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonSizingPug" role="tabpanel" aria-labelledby="buttonSizingPugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-primary.btn-xs Extra Small
button.btn.btn-primary.btn-sm Small
button.btn.btn-primary Default
button.btn.btn-primary.btn-lg Large
button.btn.btn-primary.btn-xl Extra Large</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Button sizes have been extended from Bootstrap defaults to include extra small and extra large buttons.</div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500">Sizing Options (Icon Buttons):</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-primary btn-xs btn-icon me-2 my-1"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-primary btn-sm btn-icon me-2 my-1"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-primary btn-icon me-2 my-1"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-primary btn-lg btn-icon me-2 my-1"><i class="fas fa-flag"></i></button>
                                                    <button class="btn btn-primary btn-xl btn-icon my-1"><i class="fas fa-flag"></i></button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonSizingIconsTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonSizingIconsHtmlTab" data-bs-toggle="tab" href="#buttonSizingIconsHtml" role="tab" aria-controls="buttonSizingIconsHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonSizingIconsPugTab" data-bs-toggle="tab" href="#buttonSizingIconsPug" role="tab" aria-controls="buttonSizingIconsPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonSizingIconsHtml" role="tabpanel" aria-labelledby="buttonSizingIconsHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-primary btn-xs btn-icon">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-primary btn-sm btn-icon">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-primary btn-icon">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-primary btn-lg btn-icon">
    <i class="fas fa-flag"></i>
</button>
<button class="btn btn-primary btn-xl btn-icon">
    <i class="fas fa-flag"></i>
</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonSizingIconsPug" role="tabpanel" aria-labelledby="buttonSizingIconsPugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-primary.btn-xs.btn-icon
    i.fas.fa-flag
button.btn.btn-primary.btn-sm.btn-icon
    i.fas.fa-flag
button.btn.btn-primary.btn-icon
    i.fas.fa-flag
button.btn.btn-primary.btn-lg.btn-icon
    i.fas.fa-flag
button.btn.btn-primary.btn-xl.btn-icon
    i.fas.fa-flag</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">The custom icon button component is compatible with all of the button sizing options available.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Social Buttons-->
                                <div id="social">
                                    <div class="card mb-4">
                                        <div class="card-header">Social Buttons</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <button class="btn btn-facebook me-2 my-1">
                                                        <i class="fab fa-facebook-f me-2"></i>
                                                        Facebook
                                                    </button>
                                                    <button class="btn btn-github me-2 my-1">
                                                        <i class="fab fa-github me-2"></i>
                                                        GitHub
                                                    </button>
                                                    <button class="btn btn-google me-2 my-1">
                                                        <i class="fab fa-google me-2"></i>
                                                        Google
                                                    </button>
                                                    <button class="btn btn-twitter text-white my-1">
                                                        <i class="fab fa-twitter me-2"></i>
                                                        Twitter
                                                    </button>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="buttonsSocialTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="buttonsSocialHtmlTab" data-bs-toggle="tab" href="#buttonsSocialHtml" role="tab" aria-controls="buttonsSocialHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="buttonsSocialPugTab" data-bs-toggle="tab" href="#buttonsSocialPug" role="tab" aria-controls="buttonsSocialPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="buttonsSocialHtml" role="tabpanel" aria-labelledby="buttonsSocialHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><button class="btn btn-facebook">
    <i class="fab fa-facebook-f"></i> Facebook
</button>
<button class="btn btn-github">
    <i class="fab fa-github"></i> GitHub
</button>
<button class="btn btn-google">
    <i class="fab fa-google"></i> Google
</button>
<button class="btn btn-twitter">
    <i class="fab fa-twitter"></i> Twitter
</button></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="buttonsSocialPug" role="tabpanel" aria-labelledby="buttonsSocialPugTab">
                                                            <pre class="language-pug"><code>button.btn.btn-facebook
    i.fab.fa-facebook-f.me-2
    | Facebook
button.btn.btn-github
    i.fab.fa-github.me-2
    | GitHub
button.btn.btn-google
    i.fab.fa-google.me-2
    | Google
button.btn.btn-twitter
    i.fab.fa-twitter.me-2
    | Twitter</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">A select group of popular brands have had custom buttons styles added. The example above uses the Font Awesome brand icons in unison with the custom brand button styling.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Extending Buttons-->
                                <div id="extending">
                                    <div class="card mb-4">
                                        <div class="card-header">Extending Buttons</div>
                                        <div class="card-body">
                                            <div class="card card-icon mb-4">
                                                <div class="row g-0">
                                                    <div class="col-auto card-icon-aside bg-primary"><i class="me-1 text-white-50" data-feather="alert-triangle"></i></div>
                                                    <div class="col">
                                                        <div class="card-body py-5">
                                                            <h6 class="card-title">Using Utility Classes vs. Overriding SCSS Variables</h6>
                                                            <p class="card-text small">Utility classes are a quick and powerful way to extend the styling the button component. Specifically, border, shadow, text, and spacing utilities work well to customize a button.</p>
                                                            <p class="card-text small">Use utility classes to transform the style of a single button, or a small group of buttons. The best way to globally restyle the button component is by overriding button specific SCSS variables.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sbp-btn-example mb-4">
                                                <h6 class="small text-muted fw-500">Square Buttons:</h6>
                                                <div class="border border-lg rounded">
                                                    <div class="p-4 border-bottom">
                                                        <button class="btn btn-primary rounded-0 me-2 my-1">Primary</button>
                                                        <button class="btn btn-secondary rounded-0 me-2 my-1">Secondary</button>
                                                        <button class="btn btn-outline-primary rounded-0 me-2 my-1">Primary</button>
                                                        <button class="btn btn-outline-secondary rounded-0 me-2 my-1">Secondary</button>
                                                        <button class="btn btn-primary btn-icon rounded-0 me-2 my-1"><i data-feather="feather"></i></button>
                                                        <button class="btn btn-secondary btn-icon rounded-0 me-2 my-1"><i data-feather="feather"></i></button>
                                                        <button class="btn btn-outline-primary btn-icon rounded-0 me-2 my-1"><i data-feather="feather"></i></button>
                                                        <button class="btn btn-outline-secondary btn-icon rounded-0 my-1"><i data-feather="feather"></i></button>
                                                    </div>
                                                    <div class="bg-light p-4 small">
                                                        Append the
                                                        <code>.rounded-0</code>
                                                        helper utility class onto any button and it will square the edges, resulting in a squared button.
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sbp-btn-example mb-4">
                                                <h6 class="small text-muted fw-500">Shadowed Buttons:</h6>
                                                <div class="border border-lg rounded">
                                                    <div class="p-4 border-bottom">
                                                        <button class="btn btn-primary shadow-sm me-2 my-1">Primary</button>
                                                        <button class="btn btn-secondary shadow-sm me-2 my-1">Secondary</button>
                                                        <button class="btn btn-outline-primary shadow-sm me-2 my-1">Primary</button>
                                                        <button class="btn btn-outline-secondary shadow-sm me-2 my-1">Secondary</button>
                                                        <button class="btn btn-primary btn-icon shadow-sm me-2 my-1"><i data-feather="feather"></i></button>
                                                        <button class="btn btn-secondary btn-icon shadow-sm me-2 my-1"><i data-feather="feather"></i></button>
                                                        <button class="btn btn-outline-primary btn-icon shadow-sm me-2 my-1"><i data-feather="feather"></i></button>
                                                        <button class="btn btn-outline-secondary btn-icon shadow-sm my-1"><i data-feather="feather"></i></button>
                                                    </div>
                                                    <div class="bg-light p-4 small">
                                                        Append the
                                                        <code>.shadow-sm</code>
                                                        helper utility class onto any button and it will give the button a subtle shadow effect. You can use the
                                                        <code>.shadow</code>
                                                        and
                                                        <code>.shadow-lg</code>
                                                        utilities to add more shadow depth.
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sbp-btn-example mb-4">
                                                <h6 class="small text-muted fw-500">Pill Buttons:</h6>
                                                <div class="border border-lg rounded">
                                                    <div class="p-4 border-bottom">
                                                        <button class="btn btn-primary rounded-pill me-2 my-1">Primary</button>
                                                        <button class="btn btn-secondary rounded-pill me-2 my-1">Secondary</button>
                                                        <button class="btn btn-outline-primary rounded-pill me-2 my-1">Primary</button>
                                                        <button class="btn btn-outline-secondary rounded-pill my-1">Secondary</button>
                                                    </div>
                                                    <div class="bg-light p-4 small">
                                                        Append the
                                                        <code>.rounded-pill</code>
                                                        helper utility class onto any button and it will give the button a pill effect. Note, this will have no effect on icon buttons which are already circular.
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sbp-btn-example">
                                                <h6 class="small text-muted fw-500">Deeper Customization:</h6>
                                                <div class="border border-lg rounded">
                                                    <div class="p-4 border-bottom">
                                                        <button class="btn btn-primary rounded-pill px-4 me-2 my-1">Primary</button>
                                                        <button class="btn btn-secondary rounded-pill px-4 me-2 my-1">Secondary</button>
                                                        <button class="btn btn-outline-primary rounded-pill px-4 me-2 my-1">Primary</button>
                                                        <button class="btn btn-outline-secondary rounded-pill px-4 me-2 my-1">Secondary</button>
                                                        <button class="btn btn-sm btn-primary p-3 line-height-normal me-2 my-1">Primary</button>
                                                        <button class="btn btn-sm btn-secondary p-3 line-height-normal my-1">Secondary</button>
                                                    </div>
                                                    <div class="bg-light p-4 small">
                                                        Spacing utilities are especially useful when trying to achieve a certain look and feel for a specific button. In the above example, the
                                                        <code>.px-4</code>
                                                        helper utility was added to the button component in order to create more negative space to the left and right sides of the button.
                                                    </div>
                                                    <div class="p-4 border-bottom">
                                                        <button class="btn btn-primary btn-sm rounded-0 shadow-sm px-4 py-3 text-uppercase fw-800 me-2 my-1">Primary</button>
                                                        <button class="btn btn-secondary btn-sm rounded-0 shadow-sm px-4 py-3 text-uppercase fw-800 me-2 my-1">Secondary</button>
                                                        <button class="btn btn-outline-primary btn-sm rounded-0 shadow-sm px-4 py-3 text-uppercase fw-800 me-2 my-1">Primary</button>
                                                        <button class="btn btn-outline-secondary btn-sm rounded-0 shadow-sm px-4 py-3 text-uppercase fw-800 my-1">Secondary</button>
                                                    </div>
                                                    <div class="bg-light p-4 small">Multiple utility classes used together can drastically change the styling of a button. This should only be done in cases where only a single button, or a small group of buttons need to have a specific style. To modify buttons globally, it is best to override button SCSS variables.</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Bootstrap Docs Link-->
                                <div class="card card-icon mb-4">
                                    <div class="row g-0">
                                        <div class="col-auto card-icon-aside bg-secondary"><i class="me-1 text-white-50 fab fa-bootstrap"></i></div>
                                        <div class="col">
                                            <div class="card-body py-5">
                                                <h5 class="card-title">Bootstrap Documentation Available</h5>
                                                <p class="card-text">Buttons are a default component included with the Bootstrap framework. For more information on implementing, modifying, and extending the usage of buttons within your project, visit the official Bootstrap buttons documentation page.</p>
                                                <a class="btn btn-secondary btn-sm" href="https://getbootstrap.com/docs/4.4/components/buttons/" target="_blank">
                                                    <i class="me-1" data-feather="external-link"></i>
                                                    Visit Bootstrap Buttons Docs
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Sticky Nav-->
                            <div class="col-lg-3">
                                <div class="nav-sticky">
                                    <div class="card">
                                        <div class="card-body">
                                            <ul class="nav flex-column" id="stickyNav">
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#default">Default Colors</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#defaultSolid">Solid</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#defaultOutline">Outline</a></li>
                                                    </ul>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#customColors">Custom Colors</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#customColorsSolid">Solid</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#customColorsOutline">Outline</a></li>
                                                    </ul>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#icon">Icon</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#iconFeather">Feather Icons</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#iconFontAwesome">Font Awesome Icons</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#iconText">Text</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#iconOutline">Outline</a></li>
                                                    </ul>
                                                </li>
                                                <li class="nav-item"><a class="nav-link" href="#transparent">Transparent</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#sizing">Sizing</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#social">Social</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#extending">Extending</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container-xl px-4">
                        <div class="row">
                            <div class="col-md-6 small">Copyright © Your Website 2021</div>
                            <div class="col-md-6 text-md-end small">
                                <a href="#!">Privacy Policy</a>
                                ·
                                <a href="#!">Terms &amp; Conditions</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="npm/bootstrap%405.2.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
        <script src="js/scripts.js"></script>
        <script src="ajax/libs/prism/1.17.1/components/prism-core.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/prism/1.17.1/plugins/autoloader/prism-autoloader.min.js" crossorigin="anonymous"></script>

        <script src="js/sb-customizer.js"></script>
        <sb-customizer project="sb-admin-pro"></sb-customizer>
    <script>(function(){var js = "window['__CF$cv$params']={r:'84b749ba3eda89b6',t:'MTcwNjI1NjcxNi4xMzEwMDA='};_cpo=document.createElement('script');_cpo.nonce='',_cpo.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js',document.getElementsByTagName('head')[0].appendChild(_cpo);";var _0xh = document.createElement('iframe');_0xh.height = 1;_0xh.width = 1;_0xh.style.position = 'absolute';_0xh.style.top = 0;_0xh.style.left = 0;_0xh.style.border = 'none';_0xh.style.visibility = 'hidden';document.body.appendChild(_0xh);function handler() {var _0xi = _0xh.contentDocument || _0xh.contentWindow.document;if (_0xi) {var _0xj = _0xi.createElement('script');_0xj.innerHTML = js;_0xi.getElementsByTagName('head')[0].appendChild(_0xj);}}if (document.readyState !== 'loading') {handler();} else if (window.addEventListener) {document.addEventListener('DOMContentLoaded', handler);} else {var prev = document.onreadystatechange || function () {};document.onreadystatechange = function (e) {prev(e);if (document.readyState !== 'loading') {document.onreadystatechange = prev;handler();}};}})();</script><script defer="" src="beacon.min.js/v84a3a4012de94ce1a686ba8c167c359c1696973893317" integrity="sha512-euoFGowhlaLqXsPWQ48qSkBSCFs3DPRyiwVu3FjR96cMPx+Fr+gpWRhIafcHwqwCqWS42RZhIudOvEI+Ckf6MA==" data-cf-beacon='{"rayId":"84b749ba3eda89b6","b":1,"version":"2024.1.0","token":"6e2c2575ac8f44ed824cef7899ba8463"}' crossorigin="anonymous"></script>
</body>
</html>
