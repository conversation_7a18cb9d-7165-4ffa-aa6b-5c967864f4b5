<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>MFA Verification - Dashboard</title>
    <link rel="shortcut icon" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/img/favicon.png">
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/plugins/fontawesome/css/fontawesome.min.css">
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/plugins/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/style.css">
    <style>
      .otp-input {
        width: 50px;
        height: 50px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin: 0 5px;
        border: 2px solid #ddd;
        border-radius: 8px;
      }
      .otp-input:focus {
        border-color: #007bff;
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
      .otp-container {
        display: flex;
        justify-content: center;
        margin: 20px 0;
      }
      .mfa-info {
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
      }
      .countdown {
        font-weight: bold;
        color: #ff6b6b;
      }
      .resend-section {
        text-align: center;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="main-wrapper login-body">
      <div class="login-wrapper">
        <div class="container">
          <img class="img-fluid logo-dark mb-2" src="<?php echo e(asset('vendor/dashboard')); ?>/assets/img/logo.png" alt="Logo">
          <div class="loginbox">
            <div class="login-right">
              <div class="login-right-wrap">
                <h1>Multi-Factor Authentication</h1>
                <p class="account-subtitle">Enter the verification code to continue</p>
                
                <div class="mfa-info">
                  <i class="fas fa-info-circle"></i>
                  <strong>Verification Required:</strong> We've sent a 6-digit verification code to <strong><?php echo e($email); ?></strong>
                </div>

                <form id="mfa-form">
                  <input type="hidden" name="token" value="<?php echo e($token); ?>">
                  
                  <div class="form-group">
                    <label class="form-control-label">Enter 6-Digit Code</label>
                    <div class="otp-container">
                      <input type="text" class="otp-input" maxlength="1" data-index="0">
                      <input type="text" class="otp-input" maxlength="1" data-index="1">
                      <input type="text" class="otp-input" maxlength="1" data-index="2">
                      <input type="text" class="otp-input" maxlength="1" data-index="3">
                      <input type="text" class="otp-input" maxlength="1" data-index="4">
                      <input type="text" class="otp-input" maxlength="1" data-index="5">
                    </div>
                    <input type="hidden" name="otp" id="otp-hidden">
                  </div>

                  <button class="btn btn-lg btn-block btn-primary" type="submit" id="verify-btn">
                    <span class="btn-text">Verify Code</span>
                    <span class="btn-loading" style="display: none;">
                      <i class="fas fa-spinner fa-spin"></i> Verifying...
                    </span>
                  </button>
                </form>

                <div class="resend-section">
                  <p class="mb-2">Didn't receive the code?</p>
                  <button class="btn btn-link" id="resend-btn" onclick="resendOtp()">
                    <i class="fas fa-redo"></i> Resend Code
                  </button>
                  <div id="resend-countdown" style="display: none;">
                    Resend available in <span class="countdown" id="countdown-timer">60</span> seconds
                  </div>
                </div>

                <div class="mt-3 text-center">
                  <a href="<?php echo e(route('auth.page')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Login
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/jquery-3.6.0.min.js"></script>
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/script.js"></script>
    <script src="<?php echo e(asset('vendor/sweetalert')); ?>/sweetalert.min.js"></script>

    <script>
      $(document).ready(function() {
        // Auto-request OTP when page loads
        requestOtp();

        // OTP input handling
        $('.otp-input').on('input', function() {
          const value = $(this).val();
          const index = parseInt($(this).data('index'));
          
          // Only allow numbers
          if (!/^\d*$/.test(value)) {
            $(this).val('');
            return;
          }

          // Move to next input
          if (value && index < 5) {
            $('.otp-input[data-index="' + (index + 1) + '"]').focus();
          }

          // Update hidden input
          updateOtpValue();
        });

        // Handle backspace
        $('.otp-input').on('keydown', function(e) {
          const index = parseInt($(this).data('index'));
          
          if (e.key === 'Backspace' && !$(this).val() && index > 0) {
            $('.otp-input[data-index="' + (index - 1) + '"]').focus();
          }
        });

        // Handle paste
        $('.otp-input').on('paste', function(e) {
          e.preventDefault();
          const pastedData = e.originalEvent.clipboardData.getData('text');
          const digits = pastedData.replace(/\D/g, '').slice(0, 6);
          
          $('.otp-input').each(function(index) {
            $(this).val(digits[index] || '');
          });
          
          updateOtpValue();
        });

        // Form submission
        $('#mfa-form').on('submit', function(e) {
          e.preventDefault();
          verifyOtp();
        });
      });

      function updateOtpValue() {
        let otp = '';
        $('.otp-input').each(function() {
          otp += $(this).val();
        });
        $('#otp-hidden').val(otp);
      }

      function requestOtp() {
        $.ajax({
          url: '<?php echo e(route("mfa.request-otp")); ?>',
          method: 'POST',
          data: {
            token: '<?php echo e($token); ?>',
            _token: $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            if (response.success) {
              swal('Success', 'Verification code sent successfully!', 'success');
              startResendCountdown();
              
              // Show OTP in debug mode
              if (response.otp_for_testing) {
                console.log('Debug OTP:', response.otp_for_testing);
                swal('Debug Mode', 'OTP for testing: ' + response.otp_for_testing, 'info');
              }
            } else {
              swal('Error', response.message, 'error');
            }
          },
          error: function() {
            swal('Error', 'Failed to send verification code. Please try again.', 'error');
          }
        });
      }

      function verifyOtp() {
        const otp = $('#otp-hidden').val();
        
        if (otp.length !== 6) {
          swal('Error', 'Please enter the complete 6-digit code.', 'error');
          return;
        }

        $('#verify-btn').prop('disabled', true);
        $('.btn-text').hide();
        $('.btn-loading').show();

        $.ajax({
          url: '<?php echo e(route("mfa.verify")); ?>',
          method: 'POST',
          data: {
            token: '<?php echo e($token); ?>',
            otp: otp,
            _token: $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            if (response.success) {
              swal('Success', response.message, 'success').then(() => {
                window.location.href = response.redirect_url || '/admin';
              });
            } else {
              swal('Error', response.message, 'error');
              $('.otp-input').val('').first().focus();
              $('#otp-hidden').val('');
            }
          },
          error: function() {
            swal('Error', 'Verification failed. Please try again.', 'error');
            $('.otp-input').val('').first().focus();
            $('#otp-hidden').val('');
          },
          complete: function() {
            $('#verify-btn').prop('disabled', false);
            $('.btn-text').show();
            $('.btn-loading').hide();
          }
        });
      }

      function resendOtp() {
        $('#resend-btn').prop('disabled', true);
        requestOtp();
      }

      function startResendCountdown() {
        let countdown = 60;
        $('#resend-btn').hide();
        $('#resend-countdown').show();
        
        const timer = setInterval(() => {
          countdown--;
          $('#countdown-timer').text(countdown);
          
          if (countdown <= 0) {
            clearInterval(timer);
            $('#resend-countdown').hide();
            $('#resend-btn').show().prop('disabled', false);
          }
        }, 1000);
      }
    </script>
  </body>
</html>
<?php /**PATH D:\laragon\www\sa_delivery_nontelco\resources\views/auth/mfa-verification.blade.php ENDPATH**/ ?>