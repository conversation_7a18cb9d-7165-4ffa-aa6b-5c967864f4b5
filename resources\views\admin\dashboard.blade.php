@extends('admin.layout.master')
@section('content')
<main>
    <header class="page-header page-header-dark bg-gradient-primary-to-secondary pb-10">
        <div class="container-xl px-4">
            <div class="page-header-content pt-4">
                <div class="row align-items-center justify-content-between">
                    <div class="col-auto mt-4">
                        <h1 class="page-header-title">
                            <div class="page-header-icon"><i data-feather="activity"></i></div>
                            Dashboard
                        </h1>
                        {{-- <div class="page-header-subtitle">Example dashboard overview and content summary</div> --}}
                    </div>
                    {{-- <div class="col-12 col-xl-auto mt-4">
                        <div class="input-group input-group-joined border-0" style="width: 16.5rem">
                            <span class="input-group-text"><i class="text-primary" data-feather="calendar"></i></span>
                            <input class="form-control ps-0 pointer" id="litepickerRangePlugin" placeholder="Select date range...">
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>
    </header>
    <!-- Main page content-->
    <div class="container-xl px-4 mt-n10">
        <!-- Example Colored Cards for Dashboard Demo-->
        {{-- <div class="row">
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Earnings (Monthly)</div>
                                <div class="text-lg fw-bold">$40,000</div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="calendar"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Earnings (Annual)</div>
                                <div class="text-lg fw-bold">$215,000</div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Task Completion</div>
                                <div class="text-lg fw-bold">24</div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="check-square"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Pending Requests</div>
                                <div class="text-lg fw-bold">17</div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="message-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}
        <!-- Example DataTable for Dashboard Demo-->
        <div class="card mb-4">
            <div class="card-header">Courier Shipment Request Non-Telco Merchandise</div>
                <div class="card-body">
                <!-- Filter Form -->
                <form action="{{ route('admin.showShipmentRequest') }}" method="GET" class="form-inline">
                    <div class="input-group input-group-joined" style="width: 25rem;">
                        <span class="input-group-text">
                            <i data-feather="calendar"></i>
                        </span>
                        <input class="form-control ps-0" name="daterange" id="litepickerRangePlugin" placeholder="Select date range..." />
                        <button type="submit" class="btn btn-primary">Filter</button>
                    </div>
                </form>
                <hr> <!-- Optional: Add a horizontal line for visual separation -->

                <!-- Shipment Requests Table -->
                <table id="datatablesSimple" class="table">
                    <thead>
                        <tr>
                            <th>Request Datetime</th>
                            <th>Courier Service</th>
                            <th>Shipper Name</th>
                            <th>Receiver Name</th>
                            <th>AWB</th>
                            <th>Item Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($shipmentRequest as $request)
                            <tr>
                                <td>{{ $request->request_datetime }}</td>
                                <td>{{ $request->courier_service }}</td>
                                <td>{{ $request->shipper_name }}</td>
                                <td>{{ $request->receiver_name }}</td>
                                <td>{{ $request->awb }}</td>
                                <td>{{ $request->item_description }}</td>
                                <td style="text-align: center">
                                    <a class="btn btn-primary" target="_blank" href="{{ url('admin/show-resi/') }}/{{$request->request_id}}"><i data-feather="eye"></i> View</a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</main>
@endsection