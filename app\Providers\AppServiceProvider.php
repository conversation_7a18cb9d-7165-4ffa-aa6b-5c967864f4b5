<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\MfaServiceInterface;
use App\Services\MfaService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment() !== 'production') {
            $this->app->register(\Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
        }

        // Register MFA Service
        $this->app->bind(MfaServiceInterface::class, MfaService::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
