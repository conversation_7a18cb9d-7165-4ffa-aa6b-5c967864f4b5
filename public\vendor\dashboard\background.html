﻿<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="">
        <meta name="author" content="">
        <title>Background - SB Admin Pro</title>
        <link href="css/styles.css" rel="stylesheet">
        <link rel="icon" type="image/x-icon" href="assets/img/favicon.png">
        <script data-search-pseudo-elements="" defer="" src="ajax/libs/font-awesome/6.3.0/js/all.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/feather-icons/4.29.0/feather.min.js" crossorigin="anonymous"></script>
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i data-feather="menu"></i></button>
            <!-- Navbar Brand-->
            <!-- * * Tip * * You can use text or an image for your navbar brand.-->
            <!-- * * * * * * When using an image, we recommend the SVG format.-->
            <!-- * * * * * * Dimensions: Maximum height: 32px, maximum width: 240px-->
            <a class="navbar-brand pe-3 ps-4 ps-lg-2" href="index.html">SB Admin Pro</a>
            <!-- Navbar Search Input-->
            <!-- * * Note: * * Visible only on and above the lg breakpoint-->
            <form class="form-inline me-auto d-none d-lg-block me-3">
                <div class="input-group input-group-joined input-group-solid">
                    <input class="form-control pe-0" type="search" placeholder="Search" aria-label="Search">
                    <div class="input-group-text"><i data-feather="search"></i></div>
                </div>
            </form>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto">
                <!-- Documentation Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-md-block me-3">
                    <a class="nav-link dropdown-toggle" id="navbarDropdownDocs" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <div class="fw-500">Documentation</div>
                        <i class="fas fa-chevron-right dropdown-arrow"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end py-0 me-sm-n15 me-lg-0 o-hidden animated--fade-in-up" aria-labelledby="navbarDropdownDocs">
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="book"></i></div>
                            <div>
                                <div class="small text-gray-500">Documentation</div>
                                Usage instructions and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/components" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="code"></i></div>
                            <div>
                                <div class="small text-gray-500">Components</div>
                                Code snippets and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/changelog" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="file-text"></i></div>
                            <div>
                                <div class="small text-gray-500">Changelog</div>
                                Updates and changes
                            </div>
                        </a>
                    </div>
                </li>
                <!-- Navbar Search Dropdown-->
                <!-- * * Note: * * Visible only below the lg breakpoint-->
                <li class="nav-item dropdown no-caret me-3 d-lg-none">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="searchDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="search"></i></a>
                    <!-- Dropdown - Search-->
                    <div class="dropdown-menu dropdown-menu-end p-3 shadow animated--fade-in-up" aria-labelledby="searchDropdown">
                        <form class="form-inline me-auto w-100">
                            <div class="input-group input-group-joined input-group-solid">
                                <input class="form-control pe-0" type="text" placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2">
                                <div class="input-group-text"><i data-feather="search"></i></div>
                            </div>
                        </form>
                    </div>
                </li>
                <!-- Alerts Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownAlerts" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="bell"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownAlerts">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="bell"></i>
                            Alerts Center
                        </h6>
                        <!-- Example Alert 1-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-warning"><i data-feather="activity"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 29, 2021</div>
                                <div class="dropdown-notifications-item-content-text">This is an alert message. It's nothing serious, but it requires your attention.</div>
                            </div>
                        </a>
                        <!-- Example Alert 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-info"><i data-feather="bar-chart"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 22, 2021</div>
                                <div class="dropdown-notifications-item-content-text">A new monthly report is ready. Click here to view!</div>
                            </div>
                        </a>
                        <!-- Example Alert 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-danger"><i class="fas fa-exclamation-triangle"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 8, 2021</div>
                                <div class="dropdown-notifications-item-content-text">Critical system failure, systems shutting down.</div>
                            </div>
                        </a>
                        <!-- Example Alert 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-success"><i data-feather="user-plus"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 2, 2021</div>
                                <div class="dropdown-notifications-item-content-text">New user request. Woody has requested access to the organization.</div>
                            </div>
                        </a>
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">View All Alerts</a>
                    </div>
                </li>
                <!-- Messages Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownMessages" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="mail"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownMessages">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="mail"></i>
                            Message Center
                        </h6>
                        <!-- Example Message 1  -->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-2.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Thomas Wilcox · 58m</div>
                            </div>
                        </a>
                        <!-- Example Message 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-3.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Emily Fowler · 2d</div>
                            </div>
                        </a>
                        <!-- Example Message 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-4.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Marshall Rosencrantz · 3d</div>
                            </div>
                        </a>
                        <!-- Example Message 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-5.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Colby Newton · 3d</div>
                            </div>
                        </a>
                        <!-- Footer Link-->
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">Read All Messages</a>
                    </div>
                </li>
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="assets/img/illustrations/profiles/profile-1.png"></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="assets/img/illustrations/profiles/profile-1.png">
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">Valerie Luna</div>
                                <div class="dropdown-user-details-email"><a href="cdn-cgi/l/email-protection.html" class="__cf_email__" data-cfemail="b8ced4cdd6d9f8d9d7d496dbd7d5">[email&#160;protected]</a></div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <!-- Sidenav Menu Heading (Account)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <div class="sidenav-menu-heading d-sm-none">Account</div>
                            <!-- Sidenav Link (Alerts)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="bell"></i></div>
                                Alerts
                                <span class="badge bg-warning-soft text-warning ms-auto">4 New!</span>
                            </a>
                            <!-- Sidenav Link (Messages)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="mail"></i></div>
                                Messages
                                <span class="badge bg-success-soft text-success ms-auto">2 New!</span>
                            </a>
                            <!-- Sidenav Menu Heading (Core)-->
                            <div class="sidenav-menu-heading">Core</div>
                            <!-- Sidenav Accordion (Dashboard)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseDashboards" aria-expanded="false" aria-controls="collapseDashboards">
                                <div class="nav-link-icon"><i data-feather="activity"></i></div>
                                Dashboards
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseDashboards" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPages">
                                    <a class="nav-link" href="dashboard-1.html">
                                        Default
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dashboard-2.html">Multipurpose</a>
                                    <a class="nav-link" href="dashboard-3.html">Affiliate</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Custom)-->
                            <div class="sidenav-menu-heading">Custom</div>
                            <!-- Sidenav Accordion (Pages)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapsePages" aria-expanded="false" aria-controls="collapsePages">
                                <div class="nav-link-icon"><i data-feather="grid"></i></div>
                                Pages
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapsePages" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesMenu">
                                    <!-- Nested Sidenav Accordion (Pages -> Account)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAccount" aria-expanded="false" aria-controls="pagesCollapseAccount">
                                        Account
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAccount" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="account-profile.html">Profile</a>
                                            <a class="nav-link" href="account-billing.html">Billing</a>
                                            <a class="nav-link" href="account-security.html">Security</a>
                                            <a class="nav-link" href="account-notifications.html">Notifications</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Authentication)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuth" aria-expanded="false" aria-controls="pagesCollapseAuth">
                                        Authentication
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAuth" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesAuth">
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Basic)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthBasic" aria-expanded="false" aria-controls="pagesCollapseAuthBasic">
                                                Basic
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthBasic" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-basic.html">Login</a>
                                                    <a class="nav-link" href="auth-register-basic.html">Register</a>
                                                    <a class="nav-link" href="auth-password-basic.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Social)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthSocial" aria-expanded="false" aria-controls="pagesCollapseAuthSocial">
                                                Social
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthSocial" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-social.html">Login</a>
                                                    <a class="nav-link" href="auth-register-social.html">Register</a>
                                                    <a class="nav-link" href="auth-password-social.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Error)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseError" aria-expanded="false" aria-controls="pagesCollapseError">
                                        Error
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseError" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="error-400.html">400 Error</a>
                                            <a class="nav-link" href="error-401.html">401 Error</a>
                                            <a class="nav-link" href="error-403.html">403 Error</a>
                                            <a class="nav-link" href="error-404-1.html">404 Error 1</a>
                                            <a class="nav-link" href="error-404-2.html">404 Error 2</a>
                                            <a class="nav-link" href="error-500.html">500 Error</a>
                                            <a class="nav-link" href="error-503.html">503 Error</a>
                                            <a class="nav-link" href="error-504.html">504 Error</a>
                                        </nav>
                                    </div>
                                    <a class="nav-link" href="pricing.html">Pricing</a>
                                    <a class="nav-link" href="invoice.html">Invoice</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Applications)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseApps" aria-expanded="false" aria-controls="collapseApps">
                                <div class="nav-link-icon"><i data-feather="globe"></i></div>
                                Applications
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseApps" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavAppsMenu">
                                    <!-- Nested Sidenav Accordion (Apps -> Knowledge Base)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseKnowledgeBase" aria-expanded="false" aria-controls="appsCollapseKnowledgeBase">
                                        Knowledge Base
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseKnowledgeBase" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="knowledge-base-home-1.html">Home 1</a>
                                            <a class="nav-link" href="knowledge-base-home-2.html">Home 2</a>
                                            <a class="nav-link" href="knowledge-base-category.html">Category</a>
                                            <a class="nav-link" href="knowledge-base-article.html">Article</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> User Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseUserManagement" aria-expanded="false" aria-controls="appsCollapseUserManagement">
                                        User Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseUserManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="user-management-list.html">Users List</a>
                                            <a class="nav-link" href="user-management-edit-user.html">Edit User</a>
                                            <a class="nav-link" href="user-management-add-user.html">Add User</a>
                                            <a class="nav-link" href="user-management-groups-list.html">Groups List</a>
                                            <a class="nav-link" href="user-management-org-details.html">Organization Details</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> Posts Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapsePostsManagement" aria-expanded="false" aria-controls="appsCollapsePostsManagement">
                                        Posts Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapsePostsManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="blog-management-posts-list.html">Posts List</a>
                                            <a class="nav-link" href="blog-management-create-post.html">Create Post</a>
                                            <a class="nav-link" href="blog-management-edit-post.html">Edit Post</a>
                                            <a class="nav-link" href="blog-management-posts-admin.html">Posts Admin</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Flows)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseFlows" aria-expanded="false" aria-controls="collapseFlows">
                                <div class="nav-link-icon"><i data-feather="repeat"></i></div>
                                Flows
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseFlows" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="multi-tenant-select.html">Multi-Tenant Registration</a>
                                    <a class="nav-link" href="wizard.html">Wizard</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (UI Toolkit)-->
                            <div class="sidenav-menu-heading">UI Toolkit</div>
                            <!-- Sidenav Accordion (Layout)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayouts" aria-expanded="false" aria-controls="collapseLayouts">
                                <div class="nav-link-icon"><i data-feather="layout"></i></div>
                                Layout
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseLayouts" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavLayout">
                                    <!-- Nested Sidenav Accordion (Layout -> Navigation)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutSidenavVariations" aria-expanded="false" aria-controls="collapseLayoutSidenavVariations">
                                        Navigation
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutSidenavVariations" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-static.html">Static Sidenav</a>
                                            <a class="nav-link" href="layout-dark.html">Dark Sidenav</a>
                                            <a class="nav-link" href="layout-rtl.html">RTL Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Container Options)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutContainers" aria-expanded="false" aria-controls="collapseLayoutContainers">
                                        Container Options
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutContainers" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-boxed.html">Boxed Layout</a>
                                            <a class="nav-link" href="layout-fluid.html">Fluid Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Page Headers)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsPageHeaders" aria-expanded="false" aria-controls="collapseLayoutsPageHeaders">
                                        Page Headers
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsPageHeaders" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="header-simplified.html">Simplified</a>
                                            <a class="nav-link" href="header-compact.html">Compact</a>
                                            <a class="nav-link" href="header-overlap.html">Content Overlap</a>
                                            <a class="nav-link" href="header-breadcrumbs.html">Breadcrumbs</a>
                                            <a class="nav-link" href="header-light.html">Light</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Starter Layouts)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsStarterTemplates" aria-expanded="false" aria-controls="collapseLayoutsStarterTemplates">
                                        Starter Layouts
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsStarterTemplates" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="starter-default.html">Default</a>
                                            <a class="nav-link" href="starter-minimal.html">Minimal</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Components)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseComponents" aria-expanded="false" aria-controls="collapseComponents">
                                <div class="nav-link-icon"><i data-feather="package"></i></div>
                                Components
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseComponents" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="alerts.html">Alerts</a>
                                    <a class="nav-link" href="avatars.html">Avatars</a>
                                    <a class="nav-link" href="badges.html">Badges</a>
                                    <a class="nav-link" href="buttons.html">Buttons</a>
                                    <a class="nav-link" href="cards.html">
                                        Cards
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dropdowns.html">Dropdowns</a>
                                    <a class="nav-link" href="forms.html">
                                        Forms
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="modals.html">Modals</a>
                                    <a class="nav-link" href="navigation.html">Navigation</a>
                                    <a class="nav-link" href="progress.html">Progress</a>
                                    <a class="nav-link" href="step.html">Step</a>
                                    <a class="nav-link" href="timeline.html">Timeline</a>
                                    <a class="nav-link" href="toasts.html">Toasts</a>
                                    <a class="nav-link" href="tooltips.html">Tooltips</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Utilities)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseUtilities" aria-expanded="false" aria-controls="collapseUtilities">
                                <div class="nav-link-icon"><i data-feather="tool"></i></div>
                                Utilities
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseUtilities" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="animations.html">Animations</a>
                                    <a class="nav-link" href="background.html">Background</a>
                                    <a class="nav-link" href="borders.html">Borders</a>
                                    <a class="nav-link" href="lift.html">Lift</a>
                                    <a class="nav-link" href="shadows.html">Shadows</a>
                                    <a class="nav-link" href="typography.html">Typography</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Addons)-->
                            <div class="sidenav-menu-heading">Plugins</div>
                            <!-- Sidenav Link (Charts)-->
                            <a class="nav-link" href="charts.html">
                                <div class="nav-link-icon"><i data-feather="bar-chart"></i></div>
                                Charts
                            </a>
                            <!-- Sidenav Link (Tables)-->
                            <a class="nav-link" href="tables.html">
                                <div class="nav-link-icon"><i data-feather="filter"></i></div>
                                Tables
                            </a>
                        </div>
                    </div>
                    <!-- Sidenav Footer-->
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title">Valerie Luna</div>
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-dark bg-gradient-primary-to-secondary pb-10">
                        <div class="container-xl px-4">
                            <div class="page-header-content pt-4">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mt-4">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i data-feather="droplet"></i></div>
                                            Background
                                        </h1>
                                        <div class="page-header-subtitle">Add color to elements and modify backgrounds with our background utilities!</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <!-- Main page content-->
                    <div class="container-xl px-4 mt-n10">
                        <div class="row">
                            <div class="col-lg-9">
                                <!-- Bootstrap Default Background Utilities-->
                                <div id="default">
                                    <div class="card mb-4">
                                        <div class="card-header">Bootstrap Defaults</div>
                                        <div class="card-body">
                                            <div class="border border-lg rounded">
                                                <div class="p-4 border-bottom">
                                                    <div class="bg-primary text-white p-3">.bg-primary</div>
                                                    <div class="bg-secondary text-white p-3">.bg-secondary</div>
                                                    <div class="bg-success text-white p-3">.bg-success</div>
                                                    <div class="bg-danger text-white p-3">.bg-danger</div>
                                                    <div class="bg-warning text-white p-3">.bg-warning</div>
                                                    <div class="bg-info text-white p-3">.bg-info</div>
                                                    <div class="bg-light p-3">.bg-light</div>
                                                    <div class="bg-dark text-white p-3">.bg-dark</div>
                                                    <div class="bg-white p-3">.bg-white</div>
                                                    <div class="bg-transparent p-3">.bg-transparent</div>
                                                </div>
                                                <div class="bg-light p-4 small">
                                                    The default Bootstrap background utilities are contextual by nature, and pull from the color palette defined in the
                                                    <code>../variables/_colors.scss</code>
                                                    file.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Expanded Color System Background Utilities-->
                                <div id="expanded">
                                    <div class="card mb-4">
                                        <div class="card-header">Expanded Color System</div>
                                        <div class="card-body">
                                            <div class="border border-lg rounded">
                                                <div class="p-4 border-bottom">
                                                    <div class="bg-red text-white p-3">.bg-red</div>
                                                    <div class="bg-orange text-white p-3">.bg-orange</div>
                                                    <div class="bg-yellow text-white p-3">.bg-yellow</div>
                                                    <div class="bg-green text-white p-3">.bg-green</div>
                                                    <div class="bg-teal text-white p-3">.bg-teal</div>
                                                    <div class="bg-cyan text-white p-3">.bg-cyan</div>
                                                    <div class="bg-blue text-white p-3">.bg-blue</div>
                                                    <div class="bg-indigo text-white p-3">.bg-indigo</div>
                                                    <div class="bg-purple text-white p-3">.bg-purple</div>
                                                    <div class="bg-pink text-white p-3">.bg-pink</div>
                                                </div>
                                                <div class="bg-light p-4 small">
                                                    SB Admin Pro adds non-contextual colors to the color map so they can be used in the same way you would use Bootstrap colors. These colors are also defined in the color
                                                    <code>../variables/_colors.scss</code>
                                                    file.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Custom Gradient Background Utilities-->
                                <div id="gradient">
                                    <div class="card mb-4">
                                        <div class="card-header">Custom Gradient Background</div>
                                        <div class="card-body">
                                            <div class="border border-lg rounded">
                                                <div class="p-4 border-bottom"><div class="bg-gradient-primary-to-secondary text-white p-3 py-10">.bg-gradient-primary-to-secondary</div></div>
                                                <div class="bg-light p-4 small">The custom gradient background is being used in the page headers, and can be applied anywhere you would use a normal background color utility.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Adaptive Grayscale Background Utilities-->
                                <div id="grayscale">
                                    <div class="card mb-4">
                                        <div class="card-header">Adaptive Grayscale</div>
                                        <div class="card-body">
                                            <div class="border border-lg rounded">
                                                <div class="p-4 border-bottom">
                                                    <div class="bg-white p-3">.bg-white</div>
                                                    <div class="bg-gray-100 p-3">.bg-gray-100</div>
                                                    <div class="bg-gray-200 p-3">.bg-gray-200</div>
                                                    <div class="bg-gray-300 p-3">.bg-gray-300</div>
                                                    <div class="bg-gray-400 p-3">.bg-gray-400</div>
                                                    <div class="bg-gray-500 text-white p-3">.bg-gray-500</div>
                                                    <div class="bg-gray-600 text-white p-3">.bg-gray-600</div>
                                                    <div class="bg-gray-700 text-white p-3">.bg-gray-700</div>
                                                    <div class="bg-gray-800 text-white p-3">.bg-gray-800</div>
                                                    <div class="bg-gray-900 text-white p-3">.bg-gray-900</div>
                                                    <div class="bg-black text-white p-3">.bg-black</div>
                                                </div>
                                                <div class="bg-light p-4 small">
                                                    <p>
                                                        The adaptive grayscale is a custom feature in SB Admin Pro. The grayscale is formed by setting a value to the
                                                        <code>$grayscale-base-hue</code>
                                                        variable in the
                                                        <code>../variables/_colors.scss</code>
                                                        file. The base hue, set to blue by default, is then blended with a true black and white grayscale to make a slightly tinted grayscale that is used throughout the theme.
                                                    </p>
                                                    <p class="mb-0">
                                                        You can use the above utilty classes as background colors for your elements, and you can modify the adaptive grayscale within the
                                                        <code>../variables/_colors.scss</code>
                                                        file.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Custom Background Image Utilities-->
                                <div id="images">
                                    <div class="card mb-4">
                                        <div class="card-header">Background Image Utilities</div>
                                        <div class="card-body">
                                            <!-- Background Image Cover Utility-->
                                            <h6 class="small text-muted fw-500" id="defaultSolid">Background Image (Cover):</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="bg-img-cover" style="background-image: url('assets/img/demo/demo-ocean-lg.jpg')"><div style="height: 20rem"></div></div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="backgroundImageCoverTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="backgroundImageCoverHtmlTab" data-bs-toggle="tab" href="#backgroundImageCoverHtml" role="tab" aria-controls="backgroundImageCoverHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="backgroundImageCoverPugTab" data-bs-toggle="tab" href="#backgroundImageCoverPug" role="tab" aria-controls="backgroundImageCoverPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="backgroundImageCoverHtml" role="tabpanel" aria-labelledby="backgroundImageCoverHtmlTab">
                                                            <pre class="language-markup"><code><script data-cfasync="false" src="cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script type="text/plain"><div class="bg-img-cover" style="background-image: url('path/to/image')">
    ...
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="backgroundImageCoverPug" role="tabpanel" aria-labelledby="backgroundImageCoverPugTab">
                                                            <pre class="language-pug"><code>.bg-img-cover(style='background-image: url("path/to/image")')
    | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Use the
                                                    <code>.bg-img-cover</code>
                                                    utility along with a background image set using inline CSS and the background image will cover the element it is used on.
                                                </div>
                                            </div>
                                            <!-- Background Image Repeat Utility-->
                                            <h6 class="small text-muted fw-500" id="defaultOutline">Background Image (Repeat):</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="bg-img-repeat bg-primary" style="background-image: url('assets/img/backgrounds/bg-pattern-shapes.png')"><div style="height: 20rem"></div></div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="backgroundImageRepeatTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="backgroundImageRepeatHtmlTab" data-bs-toggle="tab" href="#backgroundImageRepeatHtml" role="tab" aria-controls="backgroundImageRepeatHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="backgroundImageRepeatPugTab" data-bs-toggle="tab" href="#backgroundImageRepeatPug" role="tab" aria-controls="backgroundImageRepeatPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="backgroundImageRepeatHtml" role="tabpanel" aria-labelledby="backgroundImageRepeatHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="bg-img-repeat" style="background-image: url('path/to/pattern')">
    ...
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="backgroundImageRepeatPug" role="tabpanel" aria-labelledby="backgroundImageRepeatPugTab">
                                                            <pre class="language-pug"><code>.bg-img-repeat(style='background-image: url("path/to/pattern")')
    | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Use the
                                                    <code>.bg-img-repeat</code>
                                                    utility along with a background pattern set using inline CSS and the background image will repeat across the element it is used on. Use a background color utility along with it to add a background color to accompany the pattern.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Overlay Utility-->
                                <div id="overlay">
                                    <div class="card mb-4">
                                        <div class="card-header">Background Overlay Utility</div>
                                        <div class="card-body">
                                            <!-- Background Overlay Utility Default-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="bg-img-cover overlay" style="background-image: url('assets/img/demo/demo-ocean-lg.jpg')"><div style="height: 20rem"></div></div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="overlayTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="overlayHtmlTab" data-bs-toggle="tab" href="#overlayHtml" role="tab" aria-controls="overlayHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="overlayPugTab" data-bs-toggle="tab" href="#overlayPug" role="tab" aria-controls="overlayPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="overlayHtml" role="tabpanel" aria-labelledby="overlayHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="bg-img-cover overlay" style="background-image: url('path/to/image')">
    ...
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="overlayPug" role="tabpanel" aria-labelledby="overlayPugTab">
                                                            <pre class="language-pug"><code>.bg-img-cover.overlay(style='background-image: url("path/to/image")')
    | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Use the
                                                    <code>.overlay</code>
                                                    utility along with a background image to add an overlay to an element. The default overlay adds a 50% dark overlay to the element. There are color and opacity options available as well.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Overlay Utility Opacity-->
                                <div id="overlayOpacity">
                                    <div class="card mb-4">
                                        <div class="card-header">Overlay Opacity</div>
                                        <div class="card-body">
                                            <!-- Background Overlay Utility Opacity-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="row">
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-10" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-10</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-20" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-20</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-30" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-30</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-40" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-40</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-50" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-50</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-60" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-60</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4 mb-md-0">
                                                            <div class="bg-img-cover overlay overlay-70" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-70</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4 mb-md-0">
                                                            <div class="bg-img-cover overlay overlay-80" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-80</div></div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="bg-img-cover overlay overlay-90" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-90</div></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="overlayOpacityTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="overlayOpacityHtmlTab" data-bs-toggle="tab" href="#overlayOpacityHtml" role="tab" aria-controls="overlayOpacityHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="overlayOpacityPugTab" data-bs-toggle="tab" href="#overlayOpacityPug" role="tab" aria-controls="overlayOpacityPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="overlayOpacityHtml" role="tabpanel" aria-labelledby="overlayOpacityHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- 10% Overlay -->
<div class="bg-img-cover overlay overlay-10" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 20% Overlay -->
<div class="bg-img-cover overlay overlay-20" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 30% Overlay -->
<div class="bg-img-cover overlay overlay-30" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 40% Overlay -->
<div class="bg-img-cover overlay overlay-40" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 50% Overlay -->
<div class="bg-img-cover overlay overlay-50" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 60% Overlay -->
<div class="bg-img-cover overlay overlay-60" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 70% Overlay -->
<div class="bg-img-cover overlay overlay-70" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 80% Overlay -->
<div class="bg-img-cover overlay overlay-80" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- 90% Overlay -->
<div class="bg-img-cover overlay overlay-90" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="overlayOpacityPug" role="tabpanel" aria-labelledby="overlayOpacityPugTab">
                                                            <pre class="language-pug"><code>//- 10% Overlay
.bg-img-cover.overlay.overlay-10(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 20% Overlay
.bg-img-cover.overlay.overlay-20(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 30% Overlay
.bg-img-cover.overlay.overlay-30(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 40% Overlay
.bg-img-cover.overlay.overlay-40(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 50% Overlay
.bg-img-cover.overlay.overlay-50(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 60% Overlay
.bg-img-cover.overlay.overlay-60(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 70% Overlay
.bg-img-cover.overlay.overlay-70(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 80% Overlay
.bg-img-cover.overlay.overlay-80(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- 90% Overlay
.bg-img-cover.overlay.overlay-90(style='background-image: url("path/to/image")')
    .z-1
        | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Use the
                                                    <code>.overlay</code>
                                                    utility along with an opacity utility, 10-90, to change the opacity of the overlay.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Overlay Utility Colors-->
                                <div id="overlayColors">
                                    <div class="card mb-4">
                                        <div class="card-header">Overlay Colors</div>
                                        <div class="card-body">
                                            <!-- Background Overlay Utility Colors-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="row">
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-primary" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-primary</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-secondary" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-secondary</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4">
                                                            <div class="bg-img-cover overlay overlay-success" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-success</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4 mb-md-0">
                                                            <div class="bg-img-cover overlay overlay-info" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-info</div></div>
                                                        </div>
                                                        <div class="col-md-4 mb-4 mb-md-0">
                                                            <div class="bg-img-cover overlay overlay-warning" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-warning</div></div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="bg-img-cover overlay overlay-danger" style="background-image: url('assets/img/demo/demo-ocean-sm.jpg')"><div class="py-5 px-4 z-1 text-white">.overlay-danger</div></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="overlayColorsTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="overlayColorsHtmlTab" data-bs-toggle="tab" href="#overlayColorsHtml" role="tab" aria-controls="overlayColorsHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="overlayColorsPugTab" data-bs-toggle="tab" href="#overlayColorsPug" role="tab" aria-controls="overlayColorsPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="overlayColorsHtml" role="tabpanel" aria-labelledby="overlayColorsHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Primary Overlay -->
<div class="bg-img-cover overlay overlay-primary" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- Secondary Overlay -->
<div class="bg-img-cover overlay overlay-secondary" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- Success Overlay -->
<div class="bg-img-cover overlay overlay-success" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- Info Overlay -->
<div class="bg-img-cover overlay overlay-info" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- Warning Overlay -->
<div class="bg-img-cover overlay overlay-warning" style="background-image: url('path/to/image')">
    <div class="z-1">...</div>
</div>

<!-- Danger Overlay -->
<div class="bg-img-cover overlay overlay-danger" style="background-image: url('path/to/image')">
    <div class="z-1">...</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="overlayColorsPug" role="tabpanel" aria-labelledby="overlayColorsPugTab">
                                                            <pre class="language-pug"><code>//- Primary Overlay
.bg-img-cover.overlay.overlay-primary(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- Secondary Overlay
.bg-img-cover.overlay.overlay-secondary(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- Success Overlay
.bg-img-cover.overlay.overlay-success(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- Info Overlay
.bg-img-cover.overlay.overlay-info(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- Warning Overlay
.bg-img-cover.overlay.overlay-warning(style='background-image: url("path/to/image")')
    .z-1
        | ...

//- Danger Overlay
.bg-img-cover.overlay.overlay-dagner(style='background-image: url("path/to/image")')
    .z-1
        | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Use the
                                                    <code>.overlay</code>
                                                    utility along with an overlay color utility, for example,
                                                    <code>.overlay-primary</code>
                                                    . You can also use overlay colors that are part of the extended color system, like
                                                    <code>.overlay-red</code>
                                                    .
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Combining Overlay Utility-->
                                <div id="overlayCombining">
                                    <div class="card mb-4">
                                        <div class="card-header">Combining Overlay Utilities</div>
                                        <div class="card-body">
                                            <!-- Combining Overlay Utilities-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="bg-img-cover overlay overlay-70 overlay-primary" style="background-image: url('assets/img/demo/demo-ocean-lg.jpg')"><div style="height: 20rem"></div></div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="overlayCombiningTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="overlayCombiningHtmlTab" data-bs-toggle="tab" href="#overlayCombiningHtml" role="tab" aria-controls="overlayCombiningHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="overlayCombiningPugTab" data-bs-toggle="tab" href="#overlayCombiningPug" role="tab" aria-controls="overlayCombiningPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="overlayCombiningHtml" role="tabpanel" aria-labelledby="overlayCombiningHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="bg-img-cover overlay overlay-70 overlay-primary" style="background-image: url('path/to/image')">
    ...
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="overlayCombiningPug" role="tabpanel" aria-labelledby="overlayCombiningPugTab">
                                                            <pre class="language-pug"><code>.bg-img-cover.overlay.overlay-70.overlay-primary(style='background-image: url("path/to/image")')
    | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The overlay opacity and overlay color utilities can be used together. In the above example, we use the
                                                    <code>.overlay</code>
                                                    utility with
                                                    <code>.overlay-70</code>
                                                    and
                                                    <code>.overlay-primary</code>
                                                    .
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Sticky Navigation-->
                            <div class="col-lg-3">
                                <div class="nav-sticky">
                                    <div class="card">
                                        <div class="card-body">
                                            <ul class="nav flex-column" id="stickyNav">
                                                <li class="nav-item"><a class="nav-link" href="#default">Default</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#expanded">Expanded</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#gradient">Gradient</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#grayscale">Grayscale</a></li>
                                                <li class="nav-item"><a class="nav-link" href="#images">Images</a></li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#overlay">Overlay</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#overlayOpacity">Opacity</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#overlayColors">Colors</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#overlayCombining">Combining Overlay Utilities</a></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container-xl px-4">
                        <div class="row">
                            <div class="col-md-6 small">Copyright © Your Website 2021</div>
                            <div class="col-md-6 text-md-end small">
                                <a href="#!">Privacy Policy</a>
                                ·
                                <a href="#!">Terms &amp; Conditions</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="npm/bootstrap%405.2.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
        <script src="js/scripts.js"></script>
        <script src="ajax/libs/prism/1.17.1/components/prism-core.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/prism/1.17.1/plugins/autoloader/prism-autoloader.min.js" crossorigin="anonymous"></script>

        <script src="js/sb-customizer.js"></script>
        <sb-customizer project="sb-admin-pro"></sb-customizer>
    <script>(function(){var js = "window['__CF$cv$params']={r:'84b74a12beee89b6',t:'MTcwNjI1NjczMC4zMTcwMDA='};_cpo=document.createElement('script');_cpo.nonce='',_cpo.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js',document.getElementsByTagName('head')[0].appendChild(_cpo);";var _0xh = document.createElement('iframe');_0xh.height = 1;_0xh.width = 1;_0xh.style.position = 'absolute';_0xh.style.top = 0;_0xh.style.left = 0;_0xh.style.border = 'none';_0xh.style.visibility = 'hidden';document.body.appendChild(_0xh);function handler() {var _0xi = _0xh.contentDocument || _0xh.contentWindow.document;if (_0xi) {var _0xj = _0xi.createElement('script');_0xj.innerHTML = js;_0xi.getElementsByTagName('head')[0].appendChild(_0xj);}}if (document.readyState !== 'loading') {handler();} else if (window.addEventListener) {document.addEventListener('DOMContentLoaded', handler);} else {var prev = document.onreadystatechange || function () {};document.onreadystatechange = function (e) {prev(e);if (document.readyState !== 'loading') {document.onreadystatechange = prev;handler();}};}})();</script><script defer="" src="beacon.min.js/v84a3a4012de94ce1a686ba8c167c359c1696973893317" integrity="sha512-euoFGowhlaLqXsPWQ48qSkBSCFs3DPRyiwVu3FjR96cMPx+Fr+gpWRhIafcHwqwCqWS42RZhIudOvEI+Ckf6MA==" data-cf-beacon='{"rayId":"84b74a12beee89b6","b":1,"version":"2024.1.0","token":"6e2c2575ac8f44ed824cef7899ba8463"}' crossorigin="anonymous"></script>
</body>
</html>
