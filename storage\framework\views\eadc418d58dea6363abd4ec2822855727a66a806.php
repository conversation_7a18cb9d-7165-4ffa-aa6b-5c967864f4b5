<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
  <title>Login - Dashboard</title>
  <link rel="shortcut icon" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/img/favicon.png">
  <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/bootstrap.min.css">
  <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/plugins/fontawesome/css/fontawesome.min.css">
  <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/plugins/fontawesome/css/all.min.css">
  <link rel="stylesheet" href="<?php echo e(asset('vendor/dashboard')); ?>/assets/css/style.css">
</head>

<body>
  <div class="main-wrapper login-body">
    <div class="login-wrapper">
      <div class="container">
        <img class="img-fluid logo-dark mb-2" src="<?php echo e(asset('vendor/dashboard')); ?>/assets/img/logo.png" alt="Logo">
        <div class="loginbox">
          <div class="login-right">
            <div class="login-right-wrap">
              <h1>Login</h1>
              <p class="account-subtitle">Login to Continue</p>

              <!-- Error Messages -->
              <?php if($errors->any()): ?>
              <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <ul class="mb-0">
                  <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <li><?php echo e($error); ?></li>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
              </div>
              <?php endif; ?>

              <?php if(session('error')): ?>
              <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo e(session('error')); ?>

              </div>
              <?php endif; ?>

              <?php if(session('success')): ?>
              <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

              </div>
              <?php endif; ?>

              <form action="<?php echo e(route('auth.submit')); ?>" method="post">
                <?php echo e(csrf_field()); ?>

                <div class="form-group">
                  <label class="form-control-label">Email</label>
                  <input type="email" name="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                    value="<?php echo e(old('email')); ?>" required>
                  <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="form-group">
                  <label class="form-control-label">Password</label>
                  <div class="pass-group">
                    <input type="password" name="password"
                      class="form-control pass-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                    <span class="fas fa-eye toggle-password"></span>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                  </div>
                </div>
                <button class="btn btn-lg btn-block btn-primary" type="submit">Login</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/jquery-3.6.0.min.js"></script>
  <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/bootstrap.bundle.min.js"></script>
  <script src="<?php echo e(asset('vendor/dashboard')); ?>/assets/js/script.js"></script>
</body>

</html><?php /**PATH D:\laragon\www\sa_delivery_nontelco\resources\views/auth/login.blade.php ENDPATH**/ ?>