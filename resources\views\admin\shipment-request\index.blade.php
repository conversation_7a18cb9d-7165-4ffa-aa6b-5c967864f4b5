@extends('admin.layout.master')
@section('content')
<main>
    <header class="page-header page-header-compact page-header-light border-bottom bg-white mb-4">
        <div class="container-fluid px-4">
            <div class="page-header-content">
                <div class="row align-items-center justify-content-between pt-3">
                    <div class="col-auto mb-3">
                        <h1 class="page-header-title">
                            <div class="page-header-icon"><i data-feather="list"></i></div>
                            Shipment Request List
                        </h1>
                    </div>
                    <div class="col-12 col-xl-auto mb-3">
                        {{-- <a class="btn btn-sm btn-light text-primary" href="blog-management-create-post.html">
                            <i class="me-1" data-feather="plus"></i>
                            Create New Post
                        </a> --}}
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Main page content-->
    <div class="container-fluid px-4">
        <div class="card">
            <div class="card-body">
                <table id="datatablesSimple" class="table">
                    <thead>
                        <tr>
                            <th>Request Datetime</th>
                            <th>Warehouse ID</th>
                            <th>Courier Service</th>
                            <th>Shipper Name</th>
                            <th>Receiver Name</th>
                            <th>AWB</th>
                            <th>Item Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($shipmentRequest as $request)
                            <tr>
                                <td>{{ $request->request_datetime }}</td>
                                <td>{{ $request->warehouse_id }}</td>
                                <td>{{ $request->courier_service }}</td>
                                <td>{{ $request->shipper_name }}</td>
                                <td>{{ $request->receiver_name }}</td>
                                <td>{{ $request->awb }}</td>
                                <td>{{ $request->item_description }}</td>
                                <td>
                                    <a class="btn btn-datatable btn-icon btn-transparent-dark me-2" href="{{ route('shipment-request.edit', $request->request_id) }}"><i data-feather="edit"></i></a>
                                    <form action="{{ route('shipment-request.destroy', $request->request_id) }}" method="POST" style="display:inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-datatable btn-icon btn-transparent-dark" onclick="return confirm('Are you sure you want to delete this item?')"><i data-feather="trash-2"></i></button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</main>
@endsection