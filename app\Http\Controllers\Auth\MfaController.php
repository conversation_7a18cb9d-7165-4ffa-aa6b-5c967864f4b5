<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\MfaServiceInterface;
use App\Models\AppUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class M<PERSON><PERSON>ontroller extends Controller
{
    protected $mfaService;

    public function __construct(MfaServiceInterface $mfaService)
    {
        $this->mfaService = $mfaService;
    }

    /**
     * Show MFA verification page
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function showVerificationForm(Request $request)
    {
        $token = $request->get('token');
        
        if (!$token || !$this->mfaService->validateMfaToken($token)) {
            alert()->error('Invalid or expired MFA session. Please login again.');
            return redirect()->route('auth.page');
        }

        $email = $this->mfaService->getEmailFromToken($token);
        
        return view('auth.mfa-verification', [
            'token' => $token,
            'email' => $email
        ]);
    }

    /**
     * Request OTP for MFA verification
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function requestOtp(Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        $token = $request->get('token');
        
        if (!$this->mfaService->validateMfaToken($token)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired MFA session'
            ], 400);
        }

        $email = $this->mfaService->getEmailFromToken($token);
        $user = AppUser::where('email', $email)->first();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        $result = $this->mfaService->requestOtp($email, $user->phone ?? null);

        return response()->json($result);
    }

    /**
     * Verify OTP and complete MFA authentication
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function verifyOtp(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'otp' => 'required|string|size:6'
        ]);

        $token = $request->get('token');
        $otp = $request->get('otp');

        if (!$this->mfaService->validateMfaToken($token)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired MFA session'
                ], 400);
            }
            
            alert()->error('Invalid or expired MFA session. Please login again.');
            return redirect()->route('auth.page');
        }

        $email = $this->mfaService->getEmailFromToken($token);
        $result = $this->mfaService->validateOtp($email, $otp);

        if ($result['success']) {
            // OTP is valid, complete the authentication process
            $user = AppUser::where('email', $email)->first();
            
            if ($user) {
                // Clear MFA token
                $this->mfaService->clearMfaToken($token);
                
                // Complete login
                auth()->guard('web')->login($user);
                session(["email" => $email]);
                session(["warehouse_id" => $user->warehouse_id]);
                
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Authentication successful',
                        'redirect_url' => '/admin'
                    ]);
                }
                
                alert()->success('Login Success');
                return redirect('/admin');
            }
        }

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        alert()->error($result['message']);
        return back()->withInput();
    }

    /**
     * Resend OTP
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resendOtp(Request $request)
    {
        return $this->requestOtp($request);
    }
}
