<?php

namespace App\Http\Controllers\Auth;

use Alert;
use App\Models\AppUser; // Assuming your model is in the App\Models namespace
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\MfaServiceInterface;

class AuthController extends Controller
{
    protected $mfaService;

    public function __construct(MfaServiceInterface $mfaService)
    {
        $this->mfaService = $mfaService;
    }

    public function loginpage() {
        return view('auth.login');
    }

    public function loginUser(Request $req)
    {
        $req->validate([
            'email' => 'required|email',
            'password' => 'required|min:6',
        ], [
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'password.required' => 'Password is required.',
            'password.min' => 'Password must be at least 6 characters.',
        ]);

        $email = $req->get('email');
        $password = $req->get('password');

        $user = AppUser::where('email', $email)->first();

        if (!$user) {
            return redirect('/auth-user')->with('error', 'Account not found. Please check your email address or contact administrator.');
        }

        if (!\Hash::check($password, $user->encrypted_password)) {
            return redirect('/auth-user')->with('error', 'Invalid password. Please try again.');
        }

        // Generate MFA token and redirect to MFA verification
        $mfaToken = $this->mfaService->generateMfaToken($email);

        return redirect()->route('mfa.verify-form', ['token' => $mfaToken])
                        ->with('success', 'Please complete multi-factor authentication to continue.');
    }
}

