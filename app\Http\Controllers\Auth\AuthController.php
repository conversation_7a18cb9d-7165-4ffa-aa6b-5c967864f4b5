<?php

namespace App\Http\Controllers\Auth;

use Alert;
use App\Models\AppUser; // Assuming your model is in the App\Models namespace
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\MfaServiceInterface;

class AuthController extends Controller
{
    protected $mfaService;

    public function __construct(MfaServiceInterface $mfaService)
    {
        $this->mfaService = $mfaService;
    }

    public function loginpage() {
        return view('auth.login');
    }

    public function loginUser(Request $req)
    {
        $req->validate([
            'email' => 'required',
            'password' => 'required',
        ]);   

        $email = $req->get('email');
        $password = $req->get('password');

        $user = AppUser::where('email', $email)->first();
        if ($user && \Hash::check($password, $user->encrypted_password)) {
            // Generate MFA token and redirect to MFA verification
            $mfaToken = $this->mfaService->generateMfaToken($email);

            alert()->info('Please complete multi-factor authentication to continue.');
            return redirect()->route('mfa.verify-form', ['token' => $mfaToken]);
        } else {
            alert()->error('Email atau Password Salah!');
            return redirect('/auth-user');
        }
    }
}

