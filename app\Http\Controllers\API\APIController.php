<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\MatchSchedule;
use App\Models\MatchGroup;
use App\Models\KnockoutMatch;
use App\Models\Venue;
use App\Models\Region;
use App\Models\Journey;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class APIController extends Controller
{
    public function getHighlightArticle(Request $request) {
        $region = Region::where('slug', $request->region)->first();
        $highlightArticle = Event::where('region_id', $region->id)->where('publish_status', 'Published')->orderBy('order', 'ASC')->where('is_highlight', 1)->select('id','title', 'subtitle','slug', 'category', 'key_visual', 'key_visual_mobile', 'created_at', 'order')->get();

        return response()->json([
            'status' => 1,
            'data' => $highlightArticle
        ]);
    } 

    public function getGalleryArticle(Request $request) {
        $region = Region::where('slug', $request->region)->first();
        $galleryArticle = Event::where('region_id', $region->id)->where('category', 'Gallery')->select('id','title', 'category', 'key_visual', 'key_visual_mobile', 'gallery_date', 'drive_url')->get();

        return response()->json([
            'status' => 1,
            'data' => $galleryArticle
        ]);
    } 

    public function getLatestArticle(Request $request) {
        $region = Region::where('slug', $request->region)->first();
        $latestArticle = Event::where('region_id', $region->id)->where('publish_status', 'Published')->orderBy('order', 'ASC')->where('is_highlight', 0)->where('category', 'Article')->select('id','title', 'subtitle','slug', 'category', 'key_visual', 'key_visual_mobile', 'created_at', 'order')->latest()->take(2)->get();

        return response()->json([
            'status' => 1,
            'data' => $latestArticle
        ]);
    } 

    public function getArticleDetail(Request $request) {
        $region = Region::where('slug', $request->region)->first();
        $slug = $request->slug;
        $article = Event::where('region_id', $region->id)->where('slug', $slug)->first();

        return response()->json([
            'status' => 1,
            'data' => $article
        ]);
    }

    public function getMatchSchedule(Request $request) {
        $category = explode(',', $request->category);
        $region = Region::where('slug', $request->region)->first();
        foreach($category as $item) {
            $data[Str::lower(str_replace(' ', '', $item)).'Schedule'] = MatchSchedule::where('region_id', $region->id)->where('category', $item)->get()->groupBy('match_date');
        }
        return response()->json([
            'status' => 1,
            'data' => $data
        ]);
    }

    public function getMatchGroup(Request $request) {
        $category = explode(',', $request->category);
        $region = Region::where('slug', $request->region)->first();
        foreach($category as $item) {
            $data[Str::lower(str_replace(' ', '', $item)).'Group'] = MatchGroup::where('region_id', $region->id)->where('category', $item)->get();
        }

        return response()->json([
            'status' => 1,
            'data' => $data
        ]);
    }

    public function getKnockOutMatch(Request $request) {
        $category = explode(',', $request->category);
        $region = Region::where('slug', $request->region)->first();
        foreach($category as $item) {
            $data[Str::lower(str_replace(' ', '', $item)).'KO'] = KnockoutMatch::where('region_id', $region->id)->where('category', $item)->get();
        }

        return response()->json([
            'status' => 1,
            'data' => $data
        ]);
    }

    public function getVenue(Request $request) {
        $region = Region::where('slug', $request->region)->first();
        $data['venue'] = Venue::join('regions', 'venues.region_id', 'regions.id')
        ->where('venues.region_id', $region->id)
        ->select('regions.region_name', 'regions.event_location', 'regions.event_address', 'regions.event_latitude as latitude', 'regions.event_longitude as longitude', 'venues.key_visual', 'venues.key_visual_mobile')
        ->first();

        return response()->json([
            'status' => 1,
            'data' => $data
        ]);
    }

    public function getEvent(Request $request) {
        $today = Carbon::now('Asia/Jakarta');
        
        $nearestEvent = Region::where('event_end_date', '>=', $today)
        ->whereDate('event_start_date', '>=', $today)
        ->orderBy('event_start_date', 'asc')
        ->select('region_name as city', 'slug', 'event_name', 'event_location', 'event_start_date', 'event_end_date', 'key_visual', 'key_visual_mobile')
        ->first();

        if ($nearestEvent) {
            $upcomingEvents = Region::where('event_start_date', '>', $nearestEvent->event_end_date)
                ->orderBy('event_start_date', 'asc')
                ->select('region_name as city', 'slug', 'event_name', 'event_location', 'event_start_date', 'event_end_date', 'key_visual', 'key_visual_mobile')
                ->get();
        } else {
            $upcomingEvents = Region::where('event_start_date', '>', $today)
                ->orderBy('event_start_date', 'asc')
                ->select('region_name as city', 'slug', 'event_name', 'event_location', 'event_start_date', 'event_end_date', 'key_visual', 'key_visual_mobile')
                ->get();
        }

        $data = [
            'nearestEvent' => $nearestEvent,
            'upcomingEvents' => $upcomingEvents,
        ];

        return response()->json([
            'status' => 1,
            'data' => $data
        ]);
    }

    public function getEventJourney(Request $request) {
        $regions = Region::orderBy('event_start_date', 'DESC')->get();

        $data['events'] = [];

        foreach ($regions as $region) {
            $eventJourneys = Journey::where('region_id', $region->id)
                ->select('winner_name', 'category')
                ->get()
                ->groupBy('category');

            if ($eventJourneys->isNotEmpty()) {
                $formattedDate = Carbon::parse($region->event_start_date)->translatedFormat('F Y');

                $regionData = [
                    'city' => $region->region_name,
                    'video_url' => $region->video_url,
                    'event_date' => $formattedDate,
                    'eventJourneys' => $this->transformEventJourneys($eventJourneys),
                ];

                $data['events'][] = $regionData;
            }
        }

        return response()->json([
            'status' => 1,
            'data' => $data
        ]);
    }

    private function transformEventJourneys($eventJourneys) {
        $transformedJourneys = [];

        foreach ($eventJourneys as $category => $journeyItems) {
            $journeyData = [
                'title' => $category,
                'list' => [],
            ];

            foreach ($journeyItems as $journey) {
                $content = $journey->winner_name; 

                $journeyData['list'][] = $content;
            }

            $transformedJourneys[] = $journeyData;
        }

        return $transformedJourneys;
    }


    public function getEventDetail(Request $request) {
        $region = Region::where('slug', $request->region)->first();

        $date1 = $region->event_start_date;
        $date2 = $region->event_end_date;

        // Parse the dates using Carbon
        $carbonDate1 = Carbon::parse($date1);
        $carbonDate2 = Carbon::parse($date2);

        $formattedDateRange = $carbonDate1->day . ' - ' . $carbonDate2->day . ' ' . $carbonDate2->translatedFormat('F Y');

        $data['eventDetail'] = [
            'city' => $region->region_name,
            'location' => $region->event_location,
            'event_date' => $formattedDateRange
        ];

        return response()->json([
            'status' => 1,
            'data' => $data
        ]);
    }
}
