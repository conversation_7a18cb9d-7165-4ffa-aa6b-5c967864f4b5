<?php $__env->startSection('content'); ?>
<main>
    <header class="page-header page-header-dark bg-gradient-primary-to-secondary pb-10">
        <div class="container-xl px-4">
            <div class="page-header-content pt-4">
                <div class="row align-items-center justify-content-between">
                    <div class="col-auto mt-4">
                        <h1 class="page-header-title">
                            <div class="page-header-icon"><i data-feather="package"></i></div>
                            Warehouse Dashboard
                        </h1>
                        <div class="page-header-subtitle">Real-time shipment monitoring and management</div>
                    </div>
                    <div class="col-12 col-xl-auto mt-4">
                        <div class="input-group input-group-joined border-0" style="width: 16.5rem">
                            <span class="input-group-text"><i class="text-primary" data-feather="calendar"></i></span>
                            <input class="form-control ps-0 pointer" id="litepickerRangePlugin"
                                placeholder="Select date range...">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Main page content-->
    <div class="container-xl px-4 mt-n10">
        <!-- Warehouse Statistics Cards -->
        <div class="row">
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Total Shipments</div>
                                <div class="text-lg fw-bold"><?php echo e($totalShipments); ?></div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="package"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between small">
                        <a class="text-white stretched-link" href="<?php echo e(url('/admin')); ?>">View Details</a>
                        <div class="text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Pending Requests</div>
                                <div class="text-lg fw-bold"><?php echo e($pendingShipments); ?></div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="clock"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between small">
                        <a class="text-white stretched-link" href="<?php echo e(url('/admin')); ?>">View Pending</a>
                        <div class="text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Processed</div>
                                <div class="text-lg fw-bold"><?php echo e($processedShipments); ?></div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="check-circle"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between small">
                        <a class="text-white stretched-link" href="<?php echo e(url('/admin/shipment-request/history')); ?>">View
                            History</a>
                        <div class="text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-xl-3 mb-4">
                <div class="card bg-info text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="me-3">
                                <div class="text-white-75 small">Active Couriers</div>
                                <div class="text-lg fw-bold"><?php echo e($activeCouriers); ?></div>
                            </div>
                            <i class="feather-xl text-white-50" data-feather="truck"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between small">
                        <a class="text-white stretched-link" href="<?php echo e(url('/admin/courier-shipment-request')); ?>">View
                            Couriers</a>
                        <div class="text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Enhanced Shipment Request Dashboard -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i data-feather="package" class="me-2"></i>
                        <strong>Shipment Request Management</strong>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                            <i data-feather="refresh-cw" class="me-1"></i>Refresh
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="exportData()">
                            <i data-feather="download" class="me-1"></i>Export
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Advanced Filter Form -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">
                                    <i data-feather="filter" class="me-2"></i>Advanced Filters
                                </h6>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo e(route('admin.showShipmentRequest')); ?>" method="GET" id="filterForm">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label">Date Range</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i data-feather="calendar"></i></span>
                                                <input class="form-control" name="daterange" id="litepickerRangePlugin"
                                                    placeholder="Select date range..."
                                                    value="<?php echo e(request('daterange')); ?>" />
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Courier Service</label>
                                            <select class="form-select" name="courier_service">
                                                <option value="">All Couriers</option>
                                                <?php $__currentLoopData = $allShipments->pluck('courier_service')->unique()->filter(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $courier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($courier); ?>" <?php echo e(request('courier_service')==$courier
                                                    ? 'selected' : ''); ?>>
                                                    <?php echo e($courier); ?>

                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Status</label>
                                            <select class="form-select" name="status">
                                                <option value="">All Status</option>
                                                <option value="N" <?php echo e(request('status')=='N' ? 'selected' : ''); ?>>Pending
                                                </option>
                                                <option value="Y" <?php echo e(request('status')=='Y' ? 'selected' : ''); ?>>
                                                    Processed</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Warehouse</label>
                                            <select class="form-select" name="warehouse_id">
                                                <option value="">All Warehouses</option>
                                                <?php $__currentLoopData = $allShipments->pluck('warehouse_id')->unique()->filter(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($warehouse); ?>" <?php echo e(request('warehouse_id')==$warehouse
                                                    ? 'selected' : ''); ?>>
                                                    WH-<?php echo e($warehouse); ?>

                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i data-feather="search" class="me-1"></i>Filter
                                                </button>
                                                <a href="<?php echo e(route('admin.showShipmentRequest')); ?>"
                                                    class="btn btn-outline-secondary btn-sm">
                                                    <i data-feather="x" class="me-1"></i>Clear
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Search</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i data-feather="search"></i></span>
                                                <input type="text" class="form-control" name="search"
                                                    placeholder="Search by AWB, shipper, receiver, or item description..."
                                                    value="<?php echo e(request('search')); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Items per page</label>
                                            <select class="form-select" name="per_page">
                                                <option value="10" <?php echo e(request('per_page')=='10' ? 'selected' : ''); ?>>10
                                                </option>
                                                <option value="25" <?php echo e(request('per_page')=='25' ? 'selected' : ''); ?>>25
                                                </option>
                                                <option value="50" <?php echo e(request('per_page')=='50' ? 'selected' : ''); ?>>50
                                                </option>
                                                <option value="100" <?php echo e(request('per_page')=='100' ? 'selected' : ''); ?>>
                                                    100</option>
                                            </select>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Shipment Requests Table -->
                <div class="table-responsive">
                    <table id="datatablesSimple" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><i data-feather="calendar" class="me-1"></i>Request Date</th>
                                <th><i data-feather="truck" class="me-1"></i>Courier</th>
                                <th><i data-feather="user" class="me-1"></i>Shipper</th>
                                <th><i data-feather="map-pin" class="me-1"></i>Receiver</th>
                                <th><i data-feather="package" class="me-1"></i>AWB</th>
                                <th><i data-feather="file-text" class="me-1"></i>Item</th>
                                <th><i data-feather="activity" class="me-1"></i>Status</th>
                                <th><i data-feather="settings" class="me-1"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $shipmentRequest; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $request): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="fw-bold"><?php echo e(\Carbon\Carbon::parse($request->request_datetime)->format('d
                                        M Y')); ?></div>
                                    <small class="text-muted"><?php echo e(\Carbon\Carbon::parse($request->request_datetime)->format('H:i')); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo e($request->courier_service); ?></span>
                                </td>
                                <td>
                                    <div class="fw-bold"><?php echo e($request->shipper_name); ?></div>
                                    <small class="text-muted"><?php echo e(Str::limit($request->shipper_address, 30)); ?></small>
                                </td>
                                <td>
                                    <div class="fw-bold"><?php echo e($request->receiver_name); ?></div>
                                    <small class="text-muted"><?php echo e(Str::limit($request->receiver_address, 30)); ?></small>
                                </td>
                                <td>
                                    <code class="bg-light p-1 rounded"><?php echo e($request->awb ?: 'N/A'); ?></code>
                                </td>
                                <td>
                                    <span title="<?php echo e($request->item_description); ?>">
                                        <?php echo e(Str::limit($request->item_description, 40)); ?>

                                    </span>
                                </td>
                                <td>
                                    <?php if($request->isprint == 'Y'): ?>
                                    <span class="badge bg-success">
                                        <i data-feather="check-circle" class="me-1"></i>Processed
                                    </span>
                                    <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i data-feather="clock" class="me-1"></i>Pending
                                    </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a class="btn btn-sm btn-outline-primary" target="_blank"
                                            href="<?php echo e(url('admin/show-resi/')); ?>/<?php echo e($request->request_id); ?>"
                                            title="View Details">
                                            <i data-feather="eye"></i>
                                        </a>
                                        <?php if($request->isprint == 'N'): ?>
                                        <button class="btn btn-sm btn-outline-success"
                                            onclick="processShipment(<?php echo e($request->request_id); ?>)"
                                            title="Mark as Processed">
                                            <i data-feather="check"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($shipmentRequest->hasPages()): ?>
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        Showing <?php echo e($shipmentRequest->firstItem()); ?> to <?php echo e($shipmentRequest->lastItem()); ?> of <?php echo e($shipmentRequest->total()); ?> results
                    </div>
                    <div>
                        <?php echo e($shipmentRequest->appends(request()->query())->links()); ?>

                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
</main>

<?php $__env->startPush('scripts'); ?>
<script>
    // Enhanced dashboard functionality
    function refreshData() {
        window.location.reload();
    }

    function exportData() {
        // Get current filter parameters
        const form = document.getElementById('filterForm');
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);

        // Add export parameter
        params.append('export', 'excel');

        // Create download link
        const exportUrl = '<?php echo e(route("admin.showShipmentRequest")); ?>?' + params.toString();
        window.open(exportUrl, '_blank');
    }

    function processShipment(requestId) {
        Swal.fire({
            title: 'Process Shipment?',
            text: 'Mark this shipment request as processed?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, Process',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // AJAX call to process shipment
                fetch(`/admin/process-shipment/${requestId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('Processed!', 'Shipment has been marked as processed.', 'success')
                            .then(() => window.location.reload());
                    } else {
                        Swal.fire('Error!', data.message || 'Failed to process shipment.', 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('Error!', 'An error occurred while processing the shipment.', 'error');
                });
            }
        });
    }

    // Auto-submit form on filter changes
    document.addEventListener('DOMContentLoaded', function() {
        const selects = document.querySelectorAll('#filterForm select');
        selects.forEach(select => {
            select.addEventListener('change', function() {
                // Optional: Auto-submit on filter change
                // document.getElementById('filterForm').submit();
            });
        });

        // Initialize feather icons for dynamically loaded content
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\sa_delivery_nontelco\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>