﻿<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="">
        <meta name="author" content="">
        <title>Cards - SB Admin Pro</title>
        <link href="css/styles.css" rel="stylesheet">
        <link rel="icon" type="image/x-icon" href="assets/img/favicon.png">
        <script data-search-pseudo-elements="" defer="" src="ajax/libs/font-awesome/6.3.0/js/all.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/feather-icons/4.29.0/feather.min.js" crossorigin="anonymous"></script>
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i data-feather="menu"></i></button>
            <!-- Navbar Brand-->
            <!-- * * Tip * * You can use text or an image for your navbar brand.-->
            <!-- * * * * * * When using an image, we recommend the SVG format.-->
            <!-- * * * * * * Dimensions: Maximum height: 32px, maximum width: 240px-->
            <a class="navbar-brand pe-3 ps-4 ps-lg-2" href="index.html">SB Admin Pro</a>
            <!-- Navbar Search Input-->
            <!-- * * Note: * * Visible only on and above the lg breakpoint-->
            <form class="form-inline me-auto d-none d-lg-block me-3">
                <div class="input-group input-group-joined input-group-solid">
                    <input class="form-control pe-0" type="search" placeholder="Search" aria-label="Search">
                    <div class="input-group-text"><i data-feather="search"></i></div>
                </div>
            </form>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto">
                <!-- Documentation Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-md-block me-3">
                    <a class="nav-link dropdown-toggle" id="navbarDropdownDocs" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <div class="fw-500">Documentation</div>
                        <i class="fas fa-chevron-right dropdown-arrow"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end py-0 me-sm-n15 me-lg-0 o-hidden animated--fade-in-up" aria-labelledby="navbarDropdownDocs">
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="book"></i></div>
                            <div>
                                <div class="small text-gray-500">Documentation</div>
                                Usage instructions and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/components" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="code"></i></div>
                            <div>
                                <div class="small text-gray-500">Components</div>
                                Code snippets and reference
                            </div>
                        </a>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item py-3" href="https://docs.startbootstrap.com/sb-admin-pro/changelog" target="_blank">
                            <div class="icon-stack bg-primary-soft text-primary me-4"><i data-feather="file-text"></i></div>
                            <div>
                                <div class="small text-gray-500">Changelog</div>
                                Updates and changes
                            </div>
                        </a>
                    </div>
                </li>
                <!-- Navbar Search Dropdown-->
                <!-- * * Note: * * Visible only below the lg breakpoint-->
                <li class="nav-item dropdown no-caret me-3 d-lg-none">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="searchDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="search"></i></a>
                    <!-- Dropdown - Search-->
                    <div class="dropdown-menu dropdown-menu-end p-3 shadow animated--fade-in-up" aria-labelledby="searchDropdown">
                        <form class="form-inline me-auto w-100">
                            <div class="input-group input-group-joined input-group-solid">
                                <input class="form-control pe-0" type="text" placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2">
                                <div class="input-group-text"><i data-feather="search"></i></div>
                            </div>
                        </form>
                    </div>
                </li>
                <!-- Alerts Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownAlerts" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="bell"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownAlerts">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="bell"></i>
                            Alerts Center
                        </h6>
                        <!-- Example Alert 1-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-warning"><i data-feather="activity"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 29, 2021</div>
                                <div class="dropdown-notifications-item-content-text">This is an alert message. It's nothing serious, but it requires your attention.</div>
                            </div>
                        </a>
                        <!-- Example Alert 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-info"><i data-feather="bar-chart"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 22, 2021</div>
                                <div class="dropdown-notifications-item-content-text">A new monthly report is ready. Click here to view!</div>
                            </div>
                        </a>
                        <!-- Example Alert 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-danger"><i class="fas fa-exclamation-triangle"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 8, 2021</div>
                                <div class="dropdown-notifications-item-content-text">Critical system failure, systems shutting down.</div>
                            </div>
                        </a>
                        <!-- Example Alert 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <div class="dropdown-notifications-item-icon bg-success"><i data-feather="user-plus"></i></div>
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-details">December 2, 2021</div>
                                <div class="dropdown-notifications-item-content-text">New user request. Woody has requested access to the organization.</div>
                            </div>
                        </a>
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">View All Alerts</a>
                    </div>
                </li>
                <!-- Messages Dropdown-->
                <li class="nav-item dropdown no-caret d-none d-sm-block me-3 dropdown-notifications">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownMessages" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="mail"></i></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownMessages">
                        <h6 class="dropdown-header dropdown-notifications-header">
                            <i class="me-2" data-feather="mail"></i>
                            Message Center
                        </h6>
                        <!-- Example Message 1  -->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-2.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Thomas Wilcox · 58m</div>
                            </div>
                        </a>
                        <!-- Example Message 2-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-3.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Emily Fowler · 2d</div>
                            </div>
                        </a>
                        <!-- Example Message 3-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-4.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Marshall Rosencrantz · 3d</div>
                            </div>
                        </a>
                        <!-- Example Message 4-->
                        <a class="dropdown-item dropdown-notifications-item" href="#!">
                            <img class="dropdown-notifications-item-img" src="assets/img/illustrations/profiles/profile-5.png">
                            <div class="dropdown-notifications-item-content">
                                <div class="dropdown-notifications-item-content-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</div>
                                <div class="dropdown-notifications-item-content-details">Colby Newton · 3d</div>
                            </div>
                        </a>
                        <!-- Footer Link-->
                        <a class="dropdown-item dropdown-notifications-footer" href="#!">Read All Messages</a>
                    </div>
                </li>
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="assets/img/illustrations/profiles/profile-1.png"></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="assets/img/illustrations/profiles/profile-1.png">
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">Valerie Luna</div>
                                <div class="dropdown-user-details-email"><a href="cdn-cgi/l/email-protection.html" class="__cf_email__" data-cfemail="99eff5ecf7f8d9f8f6f5b7faf6f4">[email&#160;protected]</a></div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="#!">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <!-- Sidenav Menu Heading (Account)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <div class="sidenav-menu-heading d-sm-none">Account</div>
                            <!-- Sidenav Link (Alerts)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="bell"></i></div>
                                Alerts
                                <span class="badge bg-warning-soft text-warning ms-auto">4 New!</span>
                            </a>
                            <!-- Sidenav Link (Messages)-->
                            <!-- * * Note: * * Visible only on and above the sm breakpoint-->
                            <a class="nav-link d-sm-none" href="#!">
                                <div class="nav-link-icon"><i data-feather="mail"></i></div>
                                Messages
                                <span class="badge bg-success-soft text-success ms-auto">2 New!</span>
                            </a>
                            <!-- Sidenav Menu Heading (Core)-->
                            <div class="sidenav-menu-heading">Core</div>
                            <!-- Sidenav Accordion (Dashboard)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseDashboards" aria-expanded="false" aria-controls="collapseDashboards">
                                <div class="nav-link-icon"><i data-feather="activity"></i></div>
                                Dashboards
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseDashboards" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPages">
                                    <a class="nav-link" href="dashboard-1.html">
                                        Default
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dashboard-2.html">Multipurpose</a>
                                    <a class="nav-link" href="dashboard-3.html">Affiliate</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Custom)-->
                            <div class="sidenav-menu-heading">Custom</div>
                            <!-- Sidenav Accordion (Pages)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapsePages" aria-expanded="false" aria-controls="collapsePages">
                                <div class="nav-link-icon"><i data-feather="grid"></i></div>
                                Pages
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapsePages" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesMenu">
                                    <!-- Nested Sidenav Accordion (Pages -> Account)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAccount" aria-expanded="false" aria-controls="pagesCollapseAccount">
                                        Account
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAccount" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="account-profile.html">Profile</a>
                                            <a class="nav-link" href="account-billing.html">Billing</a>
                                            <a class="nav-link" href="account-security.html">Security</a>
                                            <a class="nav-link" href="account-notifications.html">Notifications</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Authentication)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuth" aria-expanded="false" aria-controls="pagesCollapseAuth">
                                        Authentication
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseAuth" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPagesAuth">
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Basic)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthBasic" aria-expanded="false" aria-controls="pagesCollapseAuthBasic">
                                                Basic
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthBasic" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-basic.html">Login</a>
                                                    <a class="nav-link" href="auth-register-basic.html">Register</a>
                                                    <a class="nav-link" href="auth-password-basic.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                            <!-- Nested Sidenav Accordion (Pages -> Authentication -> Social)-->
                                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseAuthSocial" aria-expanded="false" aria-controls="pagesCollapseAuthSocial">
                                                Social
                                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                            </a>
                                            <div class="collapse" id="pagesCollapseAuthSocial" data-bs-parent="#accordionSidenavPagesAuth">
                                                <nav class="sidenav-menu-nested nav">
                                                    <a class="nav-link" href="auth-login-social.html">Login</a>
                                                    <a class="nav-link" href="auth-register-social.html">Register</a>
                                                    <a class="nav-link" href="auth-password-social.html">Forgot Password</a>
                                                </nav>
                                            </div>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Pages -> Error)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#pagesCollapseError" aria-expanded="false" aria-controls="pagesCollapseError">
                                        Error
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="pagesCollapseError" data-bs-parent="#accordionSidenavPagesMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="error-400.html">400 Error</a>
                                            <a class="nav-link" href="error-401.html">401 Error</a>
                                            <a class="nav-link" href="error-403.html">403 Error</a>
                                            <a class="nav-link" href="error-404-1.html">404 Error 1</a>
                                            <a class="nav-link" href="error-404-2.html">404 Error 2</a>
                                            <a class="nav-link" href="error-500.html">500 Error</a>
                                            <a class="nav-link" href="error-503.html">503 Error</a>
                                            <a class="nav-link" href="error-504.html">504 Error</a>
                                        </nav>
                                    </div>
                                    <a class="nav-link" href="pricing.html">Pricing</a>
                                    <a class="nav-link" href="invoice.html">Invoice</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Applications)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseApps" aria-expanded="false" aria-controls="collapseApps">
                                <div class="nav-link-icon"><i data-feather="globe"></i></div>
                                Applications
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseApps" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavAppsMenu">
                                    <!-- Nested Sidenav Accordion (Apps -> Knowledge Base)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseKnowledgeBase" aria-expanded="false" aria-controls="appsCollapseKnowledgeBase">
                                        Knowledge Base
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseKnowledgeBase" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="knowledge-base-home-1.html">Home 1</a>
                                            <a class="nav-link" href="knowledge-base-home-2.html">Home 2</a>
                                            <a class="nav-link" href="knowledge-base-category.html">Category</a>
                                            <a class="nav-link" href="knowledge-base-article.html">Article</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> User Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapseUserManagement" aria-expanded="false" aria-controls="appsCollapseUserManagement">
                                        User Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapseUserManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="user-management-list.html">Users List</a>
                                            <a class="nav-link" href="user-management-edit-user.html">Edit User</a>
                                            <a class="nav-link" href="user-management-add-user.html">Add User</a>
                                            <a class="nav-link" href="user-management-groups-list.html">Groups List</a>
                                            <a class="nav-link" href="user-management-org-details.html">Organization Details</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Apps -> Posts Management)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#appsCollapsePostsManagement" aria-expanded="false" aria-controls="appsCollapsePostsManagement">
                                        Posts Management
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="appsCollapsePostsManagement" data-bs-parent="#accordionSidenavAppsMenu">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="blog-management-posts-list.html">Posts List</a>
                                            <a class="nav-link" href="blog-management-create-post.html">Create Post</a>
                                            <a class="nav-link" href="blog-management-edit-post.html">Edit Post</a>
                                            <a class="nav-link" href="blog-management-posts-admin.html">Posts Admin</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Flows)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseFlows" aria-expanded="false" aria-controls="collapseFlows">
                                <div class="nav-link-icon"><i data-feather="repeat"></i></div>
                                Flows
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseFlows" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="multi-tenant-select.html">Multi-Tenant Registration</a>
                                    <a class="nav-link" href="wizard.html">Wizard</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (UI Toolkit)-->
                            <div class="sidenav-menu-heading">UI Toolkit</div>
                            <!-- Sidenav Accordion (Layout)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayouts" aria-expanded="false" aria-controls="collapseLayouts">
                                <div class="nav-link-icon"><i data-feather="layout"></i></div>
                                Layout
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseLayouts" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavLayout">
                                    <!-- Nested Sidenav Accordion (Layout -> Navigation)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutSidenavVariations" aria-expanded="false" aria-controls="collapseLayoutSidenavVariations">
                                        Navigation
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutSidenavVariations" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-static.html">Static Sidenav</a>
                                            <a class="nav-link" href="layout-dark.html">Dark Sidenav</a>
                                            <a class="nav-link" href="layout-rtl.html">RTL Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Container Options)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutContainers" aria-expanded="false" aria-controls="collapseLayoutContainers">
                                        Container Options
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutContainers" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="layout-boxed.html">Boxed Layout</a>
                                            <a class="nav-link" href="layout-fluid.html">Fluid Layout</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Page Headers)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsPageHeaders" aria-expanded="false" aria-controls="collapseLayoutsPageHeaders">
                                        Page Headers
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsPageHeaders" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="header-simplified.html">Simplified</a>
                                            <a class="nav-link" href="header-compact.html">Compact</a>
                                            <a class="nav-link" href="header-overlap.html">Content Overlap</a>
                                            <a class="nav-link" href="header-breadcrumbs.html">Breadcrumbs</a>
                                            <a class="nav-link" href="header-light.html">Light</a>
                                        </nav>
                                    </div>
                                    <!-- Nested Sidenav Accordion (Layout -> Starter Layouts)-->
                                    <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseLayoutsStarterTemplates" aria-expanded="false" aria-controls="collapseLayoutsStarterTemplates">
                                        Starter Layouts
                                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                                    </a>
                                    <div class="collapse" id="collapseLayoutsStarterTemplates" data-bs-parent="#accordionSidenavLayout">
                                        <nav class="sidenav-menu-nested nav">
                                            <a class="nav-link" href="starter-default.html">Default</a>
                                            <a class="nav-link" href="starter-minimal.html">Minimal</a>
                                        </nav>
                                    </div>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Components)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseComponents" aria-expanded="false" aria-controls="collapseComponents">
                                <div class="nav-link-icon"><i data-feather="package"></i></div>
                                Components
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseComponents" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="alerts.html">Alerts</a>
                                    <a class="nav-link" href="avatars.html">Avatars</a>
                                    <a class="nav-link" href="badges.html">Badges</a>
                                    <a class="nav-link" href="buttons.html">Buttons</a>
                                    <a class="nav-link" href="cards.html">
                                        Cards
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="dropdowns.html">Dropdowns</a>
                                    <a class="nav-link" href="forms.html">
                                        Forms
                                        <span class="badge bg-primary-soft text-primary ms-auto">Updated</span>
                                    </a>
                                    <a class="nav-link" href="modals.html">Modals</a>
                                    <a class="nav-link" href="navigation.html">Navigation</a>
                                    <a class="nav-link" href="progress.html">Progress</a>
                                    <a class="nav-link" href="step.html">Step</a>
                                    <a class="nav-link" href="timeline.html">Timeline</a>
                                    <a class="nav-link" href="toasts.html">Toasts</a>
                                    <a class="nav-link" href="tooltips.html">Tooltips</a>
                                </nav>
                            </div>
                            <!-- Sidenav Accordion (Utilities)-->
                            <a class="nav-link collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#collapseUtilities" aria-expanded="false" aria-controls="collapseUtilities">
                                <div class="nav-link-icon"><i data-feather="tool"></i></div>
                                Utilities
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseUtilities" data-bs-parent="#accordionSidenav">
                                <nav class="sidenav-menu-nested nav">
                                    <a class="nav-link" href="animations.html">Animations</a>
                                    <a class="nav-link" href="background.html">Background</a>
                                    <a class="nav-link" href="borders.html">Borders</a>
                                    <a class="nav-link" href="lift.html">Lift</a>
                                    <a class="nav-link" href="shadows.html">Shadows</a>
                                    <a class="nav-link" href="typography.html">Typography</a>
                                </nav>
                            </div>
                            <!-- Sidenav Heading (Addons)-->
                            <div class="sidenav-menu-heading">Plugins</div>
                            <!-- Sidenav Link (Charts)-->
                            <a class="nav-link" href="charts.html">
                                <div class="nav-link-icon"><i data-feather="bar-chart"></i></div>
                                Charts
                            </a>
                            <!-- Sidenav Link (Tables)-->
                            <a class="nav-link" href="tables.html">
                                <div class="nav-link-icon"><i data-feather="filter"></i></div>
                                Tables
                            </a>
                        </div>
                    </div>
                    <!-- Sidenav Footer-->
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title">Valerie Luna</div>
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-dark bg-gradient-primary-to-secondary pb-10">
                        <div class="container-xl px-4">
                            <div class="page-header-content pt-4">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-auto mt-4">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i data-feather="layers"></i></div>
                                            Cards
                                        </h1>
                                        <div class="page-header-subtitle">Extended card components for displaying content within your application</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <!-- Main page content-->
                    <div class="container-xl px-4 mt-n10">
                        <div class="row">
                            <div class="col-lg-9">
                                <!-- Basic Card-->
                                <div id="basic">
                                    <div class="card mb-4">
                                        <div class="card-header">Basic Card</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card">
                                                        <div class="card-header">Basic Card Example</div>
                                                        <div class="card-body">This is an example of a basic card.</div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardBasicTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardBasicHtmlTab" data-bs-toggle="tab" href="#cardBasicHtml" role="tab" aria-controls="cardBasicHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardBasicPugTab" data-bs-toggle="tab" href="#cardBasicPug" role="tab" aria-controls="cardBasicPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardBasicHtml" role="tabpanel" aria-labelledby="cardBasicHtmlTab">
                                                            <pre class="language-markup"><code><script data-cfasync="false" src="cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script type="text/plain"><div class="card">
    <div class="card-header">Basic Card Example</div>
    <div class="card-body">This is an example of a basic card.</div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardBasicPug" role="tabpanel" aria-labelledby="cardBasicPugTab">
                                                            <pre class="language-pug"><code>.card
    .card-header Basic Card Example
    .card-body This is an example of a basic card.</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The example above is a basic card with a card header and a card body. You can extend the card using any of the features included with Bootstrap. You can use utility classes to extend the look and feel of cards as well.
                                                    <p class="mb-0">
                                                        <span class="font-weight-bold">Note:</span>
                                                        The card styling in SB Admin Pro looks best with a light background color due to the background color of the card header, card body, and box shadow.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Advanced Cards-->
                                <div id="advanced">
                                    <div class="card mb-4">
                                        <div class="card-header">Advanced Cards</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="advancedDropdown">Card with Dropdown in Header:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card card-header-actions">
                                                        <div class="card-header">
                                                            Header Dropdown
                                                            <div class="dropdown no-caret">
                                                                <button class="btn btn-transparent-dark btn-icon dropdown-toggle" id="dropdownMenuButton" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="text-primary" data-feather="more-vertical"></i></button>
                                                                <div class="dropdown-menu dropdown-menu-end animated--fade-in-up" aria-labelledby="dropdownMenuButton">
                                                                    <a class="dropdown-item" href="#!">Action</a>
                                                                    <a class="dropdown-item" href="#!">Another action</a>
                                                                    <a class="dropdown-item" href="#!">Something else here</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="card-body"><p class="card-text">This is an example of a custom card with a dropdown action inside of the card header. The card's overflow is set to hidden, so your card body height should exceed the height of your dropdown menu.</p></div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardAdvancedDropdownTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardAdvancedDropdownHtmlTab" data-bs-toggle="tab" href="#cardAdvancedDropdownHtml" role="tab" aria-controls="cardAdvancedDropdownHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardAdvancedDropdownPugTab" data-bs-toggle="tab" href="#cardAdvancedDropdownPug" role="tab" aria-controls="cardAdvancedDropdownPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardAdvancedDropdownHtml" role="tabpanel" aria-labelledby="cardAdvancedDropdownHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card card-header-actions">
    <div class="card-header">
        Header Dropdown
        <div class="dropdown no-caret">
            <button class="btn btn-transparent-dark btn-icon dropdown-toggle" id="dropdownMenuButton" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i data-feather="more-vertical"></i>
            </button>
            <div class="dropdown-menu dropdown-menu-right animated--fade-in-up" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item" href="#!">Action</a>
                <a class="dropdown-item" href="#!">Another action</a>
                <a class="dropdown-item" href="#!">Something else here</a>
            </div>
        </div>
    </div>
    <div class="card-body">
        ...
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardAdvancedDropdownPug" role="tabpanel" aria-labelledby="cardAdvancedDropdownPugTab">
                                                            <pre class="language-pug"><code>.card.card-header-actions
    .card-header
        | Header Dropdown
        .dropdown.no-caret
            button#dropdownMenuButton.btn.btn-transparent-dark.btn-icon.dropdown-toggle(type='button', data-bs-toggle='dropdown', aria-haspopup='true', aria-expanded='false')
                i.text-primary(data-feather='more-vertical')
            .dropdown-menu.dropdown-menu-end.animated--fade-in-up(aria-labelledby='dropdownMenuButton')
                a.dropdown-item(href='#!') Action
                a.dropdown-item(href='#!') Another action
                a.dropdown-item(href='#!') Something else here

    .card-body
        | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Append the
                                                    <code>.card-header-actions</code>
                                                    class to your
                                                    <code>.card</code>
                                                    when using two elements within the card header. This class will apply flex styling to the card header, allowing for even spacing between the text and another element.
                                                </div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="advancedIcons">Card with Icons in Header:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card card-header-actions">
                                                        <div class="card-header">
                                                            Header Icons
                                                            <div>
                                                                <button class="btn btn-pink btn-icon me-2"><i data-feather="heart"></i></button>
                                                                <button class="btn btn-teal btn-icon me-2"><i data-feather="bookmark"></i></button>
                                                                <button class="btn btn-blue btn-icon"><i data-feather="share"></i></button>
                                                            </div>
                                                        </div>
                                                        <div class="card-body"><p class="card-text">This is an example of a custom card with a group of icons inside of the card header.</p></div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardAdvancedIconsTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardAdvancedIconsHtmlTab" data-bs-toggle="tab" href="#cardAdvancedIconsHtml" role="tab" aria-controls="cardAdvancedIconsHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardAdvancedIconsPugTab" data-bs-toggle="tab" href="#cardAdvancedIconsPug" role="tab" aria-controls="cardAdvancedIconsPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardAdvancedIconsHtml" role="tabpanel" aria-labelledby="cardAdvancedIconsHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card card-header-actions mx-auto">
    <div class="card-header">
        Header Icons
        <div>
            <button class="btn btn-pink btn-icon mr-2">
                <i data-feather="heart"></i>
            </button>
            <button class="btn btn-teal btn-icon mr-2">
                <i data-feather="bookmark"></i>
            </button>
            <button class="btn btn-blue btn-icon">
                <i data-feather="share"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        ...
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardAdvancedIconsPug" role="tabpanel" aria-labelledby="cardAdvancedIconsPugTab">
                                                            <pre class="language-pug"><code>.card.card-header-actions
    .card-header
        | Header Icons
        div
            button.btn.btn-pink.btn-icon.me-2
                i(data-feather='heart')
            button.btn.btn-teal.btn-icon.me-2
                i(data-feather='bookmark')
            button.btn.btn-blue.btn-icon
                i(data-feather='share')

    .card-body
        ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    Use of Feather icons or Font Awesome icons are supported when using the above component. This CSS component also uses the
                                                    <code>.card-header-actions</code>
                                                    class.
                                                </div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="advancedButton">Card with a Button in Header:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card card-header-actions">
                                                        <div class="card-header">
                                                            Header Button
                                                            <button class="btn btn-primary btn-sm">Action</button>
                                                        </div>
                                                        <div class="card-body"><p class="card-text">This is an example of a custom card with a button inside of the card header.</p></div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardAdvancedButtonTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardAdvancedButtonHtmlTab" data-bs-toggle="tab" href="#cardAdvancedButtonHtml" role="tab" aria-controls="cardAdvancedButtonHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardAdvancedButtonPugTab" data-bs-toggle="tab" href="#cardAdvancedButtonPug" role="tab" aria-controls="cardAdvancedButtonPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardAdvancedButtonHtml" role="tabpanel" aria-labelledby="cardAdvancedButtonHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card card-header-actions">
    <div class="card-header">
        Header Button
        <button class="btn btn-primary btn-sm">Action</button>
    </div>
    <div class="card-body">
        ...
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardAdvancedButtonPug" role="tabpanel" aria-labelledby="cardAdvancedButtonPugTab">
                                                            <pre class="language-pug"><code>.card.card-header-actions
    .card-header
        | Header Button
        button.btn.btn-primary.btn-sm Action

    .card-body
        | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The button inside of the card header in the above component example uses the
                                                    <code>.btn-sm</code>
                                                    styling. The
                                                    <code>.btn-xs</code>
                                                    styling is also supported, but larger buttons may change the internal height of the card header.
                                                </div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="advancedInput">Card with an Input in Header:</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card card-header-actions">
                                                        <div class="card-header">
                                                            Header Input
                                                            <form><input class="form-control" placeholder="Search"></form>
                                                        </div>
                                                        <div class="card-body"><p class="card-text">This is an example of a custom card with a form input inside of the card header.</p></div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardAdvancedInputTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardAdvancedInputHtmlTab" data-bs-toggle="tab" href="#cardAdvancedInputHtml" role="tab" aria-controls="cardAdvancedInputHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardAdvancedInputPugTab" data-bs-toggle="tab" href="#cardAdvancedInputPug" role="tab" aria-controls="cardAdvancedInputPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardAdvancedInputHtml" role="tabpanel" aria-labelledby="cardAdvancedInputHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card card-header-actions">
    <div class="card-header">
        Header Input
        <form>
            <input class="form-control" placeholder="Search">
        </form>
    </div>
    <div class="card-body">
        ...
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardAdvancedInputPug" role="tabpanel" aria-labelledby="cardAdvancedInputPugTab">
                                                            <pre class="language-pug"><code>.card.card-header-actions
    .card-header
        | Header Input
        form
            input.form-control(placeholder='Search')

    .card-body
        ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The input header option uses a customized form input. This option also requires the
                                                    <code>.card-header-actions</code>
                                                    class in order to position the card header elements correctly.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card card-icon mb-4">
                                    <div class="row g-0">
                                        <div class="col-auto card-icon-aside bg-info"><i class="text-white-50" data-feather="alert-triangle"></i></div>
                                        <div class="col">
                                            <div class="card-body py-5">
                                                <h5 class="card-title">Extending Advanced Cards</h5>
                                                <p class="card-text">
                                                    The
                                                    <code>.card-header-actions</code>
                                                    class allows you to extend the content of the hard header significantly. The above examples are just a few of many ways you can add more actionable items to your card headers.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Collapsable Card-->
                                <div id="collapsable">
                                    <div class="card mb-4">
                                        <div class="card-header">Collapsable Card</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card card-collapsable">
                                                        <a class="card-header" href="#collapseCardExample" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="collapseCardExample">
                                                            Collapsable Card Example
                                                            <div class="card-collapsable-arrow"><i class="fas fa-chevron-down"></i></div>
                                                        </a>
                                                        <div class="collapse show" id="collapseCardExample">
                                                            <div class="card-body">
                                                                <p class="card-text">
                                                                    This is a collapsable card example using Bootstrap's built in collapse functionality with some added custom styling.
                                                                    <strong>Click on the card header</strong>
                                                                    to see the card body collapse and expand!
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardCollapsableTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardCollapsableHtmlTab" data-bs-toggle="tab" href="#cardCollapsableHtml" role="tab" aria-controls="cardCollapsableHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardCollapsablePugTab" data-bs-toggle="tab" href="#cardCollapsablePug" role="tab" aria-controls="cardCollapsablePug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardCollapsableHtml" role="tabpanel" aria-labelledby="cardCollapsableHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card card-collapsable">
    <a class="card-header" href="#collapseCardExample" data-bs-toggle="collapse" role="button" aria-expanded="true" aria-controls="collapseCardExample">Collapsable Card Example
        <div class="card-collapsable-arrow">
            <i class="fas fa-chevron-down"></i>
        </div>
    </a>
    <div class="collapse show" id="collapseCardExample">
        <div class="card-body">
            ...
        </div>
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardCollapsablePug" role="tabpanel" aria-labelledby="cardCollapsablePugTab">
                                                            <pre class="language-pug"><code>.card.card-collapsable
    a.card-header(href='#collapseCardExample', data-bs-toggle='collapse', role='button', aria-expanded='true', aria-controls='collapseCardExample')
        | Collapsable Card Example
        .card-collapsable-arrow
            i.fas.fa-chevron-down
    #collapseCardExample.collapse.show
        .card-body
            | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The collapsable card component uses Bootstrap's built-in collapse functionality. To learn more about using collapse, visit the
                                                    <a href="https://getbootstrap.com/docs/4.4/components/collapse/" target="_blank">collapse section of the Bootstrap Docs</a>
                                                    .
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Card Navigation-->
                                <div id="navigation">
                                    <div class="card mb-4">
                                        <div class="card-header">Navigation Cards</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="navTabs">Card with Tabbed Navigation:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <ul class="nav nav-tabs card-header-tabs" id="cardTab" role="tablist">
                                                                <li class="nav-item"><a class="nav-link active" id="overview-tab" href="#overview" data-bs-toggle="tab" role="tab" aria-controls="overview" aria-selected="true">Overview</a></li>
                                                                <li class="nav-item"><a class="nav-link" id="example-tab" href="#example" data-bs-toggle="tab" role="tab" aria-controls="example" aria-selected="false">Example</a></li>
                                                                <li class="nav-item"><a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Disabled</a></li>
                                                            </ul>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="tab-content" id="cardTabContent">
                                                                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                                                                    <h5 class="card-title">Tabbed Navigation Card</h5>
                                                                    <p class="card-text">The tabbed navigation card allows you to use Bootstrap's built in nav component to toggle between tab panes to display content.</p>
                                                                </div>
                                                                <div class="tab-pane fade" id="example" role="tabpanel" aria-labelledby="example-tab">
                                                                    <h5 class="card-title">Example Pane</h5>
                                                                    <p class="card-text">This is an example of another tab pane that you could use within a card with navigation in the header.</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardNavTabbedTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardNavTabbedHtmlTab" data-bs-toggle="tab" href="#cardNavTabbedHtml" role="tab" aria-controls="cardNavTabbedHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardNavTabbedPugTab" data-bs-toggle="tab" href="#cardNavTabbedPug" role="tab" aria-controls="cardNavTabbedPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardNavTabbedHtml" role="tabpanel" aria-labelledby="cardNavTabbedHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card">
    <div class="card-header border-bottom">
        <ul class="nav nav-tabs card-header-tabs" id="cardTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="overview-tab" href="#overview" data-bs-toggle="tab" role="tab" aria-controls="overview" aria-selected="true">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="example-tab" href="#example" data-bs-toggle="tab" role="tab" aria-controls="example" aria-selected="false">Example</a>
            </li>
            <li class="nav-item">
                <a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Disabled</a>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="cardTabContent">
            <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                <h5 class="card-title">Tabbed Navigation Card</h5>
                <p class="card-text">...</p>
            </div>
            <div class="tab-pane fade" id="example" role="tabpanel" aria-labelledby="example-tab">
                <h5 class="card-title">Example Pane</h5>
                <p class="card-text">...</p>
            </div>
        </div>
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardNavTabbedPug" role="tabpanel" aria-labelledby="cardNavTabbedPugTab">
                                                            <pre class="language-pug"><code>.card
    .card-header.border-bottom
        ul.nav.nav-tabs.card-header-tabs#cardTab(role='tablist')
            li.nav-item
                a.nav-link.active#overview-tab(href='#overview', data-bs-toggle='tab', role='tab', aria-controls='overview', aria-selected='true') Overview
            li.nav-item
                a.nav-link#example-tab(href='#example', data-bs-toggle='tab', role='tab', aria-controls='example', aria-selected='false') Example
            li.nav-item
                a.nav-link.disabled(href='#', tabindex='-1', aria-disabled='true') Disabled
    .card-body
        .tab-content#cardTabContent
            .tab-pane.fade.show.active#overview(role='tabpanel', aria-labelledby='overview-tab')
                h5.card-title Tabbed Navigation Card
                p.card-text
                    | ...
            .tab-pane.fade#example(role='tabpanel', aria-labelledby='example-tab')
                h5.card-title Example Pane
                p.card-text
                    | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    This component uses Bootstrap's tabbed navigation JavaScript behavior. For more usage information, visit the
                                                    <a href="https://getbootstrap.com/docs/4.4/components/navs/#javascript-behavior" target="_blank">Navs section of the Bootstrap Docs</a>
                                                    .
                                                </div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="navPills">Card with Pill Navigation:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <ul class="nav nav-pills card-header-pills" id="cardPill" role="tablist">
                                                                <li class="nav-item"><a class="nav-link active" id="overview-pill" href="#overviewPill" data-bs-toggle="tab" role="tab" aria-controls="overview" aria-selected="true">Overview</a></li>
                                                                <li class="nav-item"><a class="nav-link" id="example-pill" href="#examplePill" data-bs-toggle="tab" role="tab" aria-controls="example" aria-selected="false">Example</a></li>
                                                                <li class="nav-item"><a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Disabled</a></li>
                                                            </ul>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="tab-content" id="cardPillContent">
                                                                <div class="tab-pane fade show active" id="overviewPill" role="tabpanel" aria-labelledby="overview-pill">
                                                                    <h5 class="card-title">Pill Navigation Card</h5>
                                                                    <p class="card-text">The pill navigation card allows you to use Bootstrap's built in nav component to toggle between tab panes to display content using nav pills in the card header.</p>
                                                                </div>
                                                                <div class="tab-pane fade" id="examplePill" role="tabpanel" aria-labelledby="example-pill">
                                                                    <h5 class="card-title">Example Pane</h5>
                                                                    <p class="card-text">This is an example of another tab pane that you could use within a card with navigation in the header.</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardNavPillsTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardNavPillsHtmlTab" data-bs-toggle="tab" href="#cardNavPillsHtml" role="tab" aria-controls="cardNavPillsHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardNavPillsPugTab" data-bs-toggle="tab" href="#cardNavPillsPug" role="tab" aria-controls="cardNavPillsPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardNavPillsHtml" role="tabpanel" aria-labelledby="cardNavPillsHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card">
    <div class="card-header">
        <ul class="nav nav-pills card-header-pills" id="cardPill" role="tablist">
            <li class="nav-item"><a class="nav-link active" id="overview-pill" href="#overviewPill" data-bs-toggle="tab" role="tab" aria-controls="overview" aria-selected="true">Overview</a></li>
            <li class="nav-item"><a class="nav-link" id="example-pill" href="#examplePill" data-bs-toggle="tab" role="tab" aria-controls="example" aria-selected="false">Example</a></li>
            <li class="nav-item"><a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Disabled</a></li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="cardPillContent">
            <div class="tab-pane fade show active" id="overviewPill" role="tabpanel" aria-labelledby="overview-pill">
                <h5 class="card-title">Pill Navigation Card</h5>
                <p class="card-text">...</p>
            </div>
            <div class="tab-pane fade" id="examplePill" role="tabpanel" aria-labelledby="example-pill">
                <h5 class="card-title">Example Pane</h5>
                <p class="card-text">...</p>
            </div>
        </div>
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardNavPillsPug" role="tabpanel" aria-labelledby="cardNavPillsPugTab">
                                                            <pre class="language-pug"><code>.card
    .card-header
        ul.nav.nav-pills.card-header-pills#cardPill(role='tablist')
            li.nav-item
                a.nav-link.active#overview-pill(href='#overviewPill', data-bs-toggle='tab', role='tab', aria-controls='overview', aria-selected='true') Overview
            li.nav-item
                a.nav-link#example-pill(href='#examplePill', data-bs-toggle='tab', role='tab', aria-controls='example', aria-selected='false') Example
            li.nav-item
                a.nav-link.disabled(href='#', tabindex='-1', aria-disabled='true') Disabled
    .card-body
        .tab-content#cardPillContent
            .tab-pane.fade.show.active#overviewPill(role='tabpanel', aria-labelledby='overview-pill')
                h5.card-title Pill Navigation Card
                p.card-text 
                    | ...
            .tab-pane.fade#examplePill(role='tabpanel', aria-labelledby='example-pill')
                h5.card-title Example Pane
                p.card-text 
                    | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    This component uses Bootstrap's tabbed navigation JavaScript behavior. For more usage information, visit the
                                                    <a href="https://getbootstrap.com/docs/4.4/components/navs/#javascript-behavior" target="_blank">Navs section of the Bootstrap Docs</a>
                                                    .
                                                </div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="navPillsVertical">Card with Vertically Stacked Pill Navigation:</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card">
                                                        <div class="card-header">Vertical Pill Navigation</div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-3">
                                                                    <ul class="nav nav-pills flex-column" id="cardPillVertical" role="tablist">
                                                                        <li class="nav-item"><a class="nav-link active" id="overview-pill-vertical" href="#overviewPillVertical" data-bs-toggle="tab" role="tab" aria-controls="overview" aria-selected="true">Overview</a></li>
                                                                        <li class="nav-item"><a class="nav-link" id="example-pill-vertical" href="#examplePillVertical" data-bs-toggle="tab" role="tab" aria-controls="example" aria-selected="false">Example</a></li>
                                                                        <li class="nav-item"><a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Disabled</a></li>
                                                                    </ul>
                                                                </div>
                                                                <div class="col-md-9">
                                                                    <div class="tab-content" id="cardPillContentVertical">
                                                                        <div class="tab-pane fade show active" id="overviewPillVertical" role="tabpanel" aria-labelledby="overview-pill-vertical">
                                                                            <h5 class="card-title">Vertical Pill Navigation Card</h5>
                                                                            <p class="card-text">The tabbed navigation card allows you to use Bootstrap's built in nav component to toggle between tab panes to display content.</p>
                                                                        </div>
                                                                        <div class="tab-pane fade" id="examplePillVertical" role="tabpanel" aria-labelledby="example-pill-vertical">
                                                                            <h5 class="card-title">Example Pane</h5>
                                                                            <p class="card-text">This is an example of another tab pane that you could use within a card with navigation in the header.</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardNavPillsVertTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardNavPillsVertHtmlTab" data-bs-toggle="tab" href="#cardNavPillsVertHtml" role="tab" aria-controls="cardNavPillsVertHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardNavPillsVertPugTab" data-bs-toggle="tab" href="#cardNavPillsVertPug" role="tab" aria-controls="cardNavPillsVertPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardNavPillsVertHtml" role="tabpanel" aria-labelledby="cardNavPillsVertHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card">
    <div class="card-header">Vertical Pill Navigation</div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <ul class="nav nav-pills flex-column" id="cardPillVertical" role="tablist">
                    <li class="nav-item"><a class="nav-link active" id="overview-pill-vertical" href="#overviewPillVertical" data-bs-toggle="tab" role="tab" aria-controls="overview" aria-selected="true">Overview</a></li>
                    <li class="nav-item"><a class="nav-link" id="example-pill-vertical" href="#examplePillVertical" data-bs-toggle="tab" role="tab" aria-controls="example" aria-selected="false">Example</a></li>
                    <li class="nav-item"><a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Disabled</a></li>
                </ul>
            </div>
            <div class="col-md-9">
                <div class="tab-content" id="cardPillContentVertical">
                    <div class="tab-pane fade show active" id="overviewPillVertical" role="tabpanel" aria-labelledby="overview-pill-vertical">
                        <h5 class="card-title">Vertical Pill Navigation Card</h5>
                        <p class="card-text">...</p>
                    </div>
                    <div class="tab-pane fade" id="examplePillVertical" role="tabpanel" aria-labelledby="example-pill-vertical">
                        <h5 class="card-title">Example Pane</h5>
                        <p class="card-text">...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardNavPillsVertPug" role="tabpanel" aria-labelledby="cardNavPillsVertPugTab">
                                                            <pre class="language-pug"><code>.card
    .card-header Vertical Pill Navigation
    .card-body
        .row
            .col-md-3
                ul.nav.nav-pills.flex-column#cardPillVertical(role='tablist')
                    li.nav-item
                        a.nav-link.active#overview-pill-vertical(href='#overviewPillVertical', data-bs-toggle='tab', role='tab', aria-controls='overview', aria-selected='true') Overview
                    li.nav-item
                        a.nav-link#example-pill-vertical(href='#examplePillVertical', data-bs-toggle='tab', role='tab', aria-controls='example', aria-selected='false') Example
                    li.nav-item
                        a.nav-link.disabled(href='#', tabindex='-1', aria-disabled='true') Disabled
            .col-md-9
                .tab-content#cardPillContentVertical
                    .tab-pane.fade.show.active#overviewPillVertical(role='tabpanel', aria-labelledby='overview-pill-vertical')
                        h5.card-title Vertical Pill Navigation Card
                        p.card-text
                            | ...
                    .tab-pane.fade#examplePillVertical(role='tabpanel', aria-labelledby='example-pill-vertical')
                        h5.card-title Example Pane
                        p.card-text
                            | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">The vertical pills card component uses Bootstrap layout classes inside of the card body in order to acheive the vertically stacked pill navigation.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Scrollable Card-->
                                <div id="scrollable">
                                    <div class="card mb-4">
                                        <div class="card-header">Scrollable Card</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content bg-light">
                                                    <div class="card card-scrollable">
                                                        <div class="card-header">Scrollable Card Example</div>
                                                        <div class="card-body">
                                                            <p class="card-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                                                            <p class="card-text">Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                                                        </div>
                                                        <div class="card-footer small text-muted">Place cursor over card body and scroll!</div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardScrollableTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardScrollableHtmlTab" data-bs-toggle="tab" href="#cardScrollableHtml" role="tab" aria-controls="cardScrollableHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardScrollablePugTab" data-bs-toggle="tab" href="#cardScrollablePug" role="tab" aria-controls="cardScrollablePug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardScrollableHtml" role="tabpanel" aria-labelledby="cardScrollableHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><div class="card card-scrollable">
    <div class="card-header">Scrollable Card Example</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer small text-muted">Card Footer</div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardScrollablePug" role="tabpanel" aria-labelledby="cardScrollablePugTab">
                                                            <pre class="language-pug"><code>.card.card-scrollable
    .card-header Scrollable Card Example
    .card-body
        p.card-text
            ...
    .card-footer.small.text-muted Card Footer</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The custom scrollable card allows you to set a maximum height to the
                                                    <code>.card-body</code>
                                                    element, and scroll the contents of the card body when the height is fixed. You can set the max height of the card body by changing the
                                                    <code>$card-scrollable-max-height</code>
                                                    SCSS variable, which is set to
                                                    <code>15rem</code>
                                                    by default. You can also set the max height of the card body using inline CSS, or CSS overrides for different cards.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Image Card-->
                                <div id="image">
                                    <div class="card mb-4">
                                        <div class="card-header">Image Cards</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="imageCaps">Card Image Caps:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="card">
                                                                <img class="card-img-top" src="assets/img/demo/cards/card-img-top.jpg" alt="...">
                                                                <div class="card-body">
                                                                    <h5 class="card-title">Card Image Cap (Top)</h5>
                                                                    <p class="card-text">This is am example of a default Bootstrap image card with an image above the card body content.</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="card">
                                                                <div class="card-body">
                                                                    <h5 class="card-title">Card Image Cap (Bottom)</h5>
                                                                    <p class="card-text">This is am example of a default Bootstrap image card with an image below the card body content.</p>
                                                                </div>
                                                                <img class="card-img-bottom" src="assets/img/demo/cards/card-img-bottom.jpg" alt="...">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardImageCapsTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardImageCapsHtmlTab" data-bs-toggle="tab" href="#cardImageCapsHtml" role="tab" aria-controls="cardImageCapsHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardImageCapsPugTab" data-bs-toggle="tab" href="#cardImageCapsPug" role="tab" aria-controls="cardImageCapsPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardImageCapsHtml" role="tabpanel" aria-labelledby="cardImageCapsHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Card Image Cap (Top) Example -->
<div class="card">
    <img class="card-img-top" src="path/to/image" alt="...">
    <div class="card-body">
        <h5 class="card-title">Card Image Cap (Top)</h5>
        <p class="card-text">...</p>
    </div>
</div>

<!-- Card Image Cap (Bottom) Example -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">Card Image Cap (Bottom)</h5>
        <p class="card-text">...</p>
    </div>
    <img class="card-img-bottom" src="path/to/image" alt="...">
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardImageCapsPug" role="tabpanel" aria-labelledby="cardImageCapsPugTab">
                                                            <pre class="language-pug"><code>//- Card Image Cap (Top) Example
.card
    img.card-img-top(src='path/to/image', alt='...')
    .card-body
        h5.card-title Card Image Cap (Top)
        p.card-text
            | ...

//- Card Image Cap (Bottom) Example
.card
    .card-body
        h5.card-title Card Image Cap (Bottom)
        p.card-text
            | ...
    img.card-img-bottom(src='path/to/image', alt='...')</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Card image caps are included with Bootstrap by default. When adding your image, make sure to correctly point to the path of the image being used.</div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="imageOverlay">Card Image Overlay:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="card bg-light text-dark">
                                                        <img class="card-img" src="assets/img/demo/cards/card-img-overlay.jpg" alt="...">
                                                        <div class="card-img-overlay">
                                                            <h5 class="card-title">Card Image (Overlay)</h5>
                                                            <p class="card-text">This card variant uses a background image as the card image with a text overlay. You can use text utility classes to style the text color.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardImageOverlayTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardImageOverlayHtmlTab" data-bs-toggle="tab" href="#cardImageOverlayHtml" role="tab" aria-controls="cardImageOverlayHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardImageOverlayPugTab" data-bs-toggle="tab" href="#cardImageOverlayPug" role="tab" aria-controls="cardImageOverlayPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardImageOverlayHtml" role="tabpanel" aria-labelledby="cardImageOverlayHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Card Image Overlay Example -->
<div class="card">
    <img class="card-img" src="path/to/image" alt="...">
    <div class="card-img-overlay">
        <h5 class="card-title">Card Image (Overlay)</h5>
        <p class="card-text">...</p>
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardImageOverlayPug" role="tabpanel" aria-labelledby="cardImageOverlayPugTab">
                                                            <pre class="language-pug"><code>//- Card Image Overlay Example
.card
    img.card-img(src='path/to/image', alt='...')
    .card-img-overlay
        h5.card-title Card Image (Overlay)
        p.card-text
            | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">When using an image overlay card, you can use text utilities to style the content within the card, or custom CSS. In the above example, text color is set to dark to account for a lighter background image.</div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="imageSide">Card Image Sides:</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="card">
                                                                <div class="row g-0">
                                                                    <div class="col-md-4"><img class="img-fluid" src="assets/img/demo/cards/card-img-left.jpg" alt="..."></div>
                                                                    <div class="col-md-8">
                                                                        <div class="card-body">
                                                                            <h5 class="card-title">Card Image (Left)</h5>
                                                                            <p class="card-text">Use the Bootstrap grid with utility classes to create this card variant.</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="card">
                                                                <div class="row g-0">
                                                                    <div class="col-md-8">
                                                                        <div class="card-body">
                                                                            <h5 class="card-title">Card Image (Right)</h5>
                                                                            <p class="card-text">Use the Bootstrap grid with utility classes to create this card variant.</p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-4"><img class="img-fluid" src="assets/img/demo/cards/card-img-right.jpg" alt="..."></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardImageSidesTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardImageSidesHtmlTab" data-bs-toggle="tab" href="#cardImageSidesHtml" role="tab" aria-controls="cardImageSidesHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardImageSidesPugTab" data-bs-toggle="tab" href="#cardImageSidesPug" role="tab" aria-controls="cardImageSidesPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardImageSidesHtml" role="tabpanel" aria-labelledby="cardImageSidesHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Card Image (Left) -->
<div class="card">
    <div class="row no-gutters">
        <div class="col-md-4"><img class="img-fluid" src="path/to/image" alt="..."></div>
        <div class="col-md-8">
            <div class="card-body">
                <h5 class="card-title">Card Image (Left)</h5>
                <p class="card-text">...</p>
            </div>
        </div>
    </div>
</div>

<!-- Card Image (Right) -->
<div class="card">
    <div class="row no-gutters">
        <div class="col-md-8">
            <div class="card-body">
                <h5 class="card-title">Card Image (Right)</h5>
                <p class="card-text">...</p>
            </div>
        </div>
        <div class="col-md-4"><img class="img-fluid" src="path/to/image" alt="..."></div>
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardImageSidesPug" role="tabpanel" aria-labelledby="cardImageSidesPugTab">
                                                            <pre class="language-pug"><code>//- Card Image (Left)
.card
    .row.g-0
        .col-md-4
            img.img-fluid(src='path/to/image', alt='...')
        .col-md-8
            .card-body
                h5.card-title Card Image (Left)
                p.card-text
                    | ...

//- Card Image (Right)
.card
    .row.g-0
        .col-md-8
            .card-body
                h5.card-title Card Image (Right)
                p.card-text
                    | ...
        .col-md-4
            img.img-fluid(src='path/to/image', alt='...')</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">The Bootstrap grid is utilized to create the above card variants. The above examples show card images on either side of the card body.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Styled Card-->
                                <div id="styled">
                                    <div class="card mb-4">
                                        <div class="card-header">Styled Cards</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="styledWaves">Waves:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="card card-waves">
                                                        <div class="card-header">Waves Styled Card</div>
                                                        <div class="card-body">
                                                            <div class="py-10">
                                                                This is an example of a card with the waves styling applied. Use
                                                                <code>.card-waves</code>
                                                                to set the background image to the waved styling. This works best with default white cards, and with cards that do not have a card footer.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardStyledWavesTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardStyledWavesHtmlTab" data-bs-toggle="tab" href="#cardStyledWavesHtml" role="tab" aria-controls="cardStyledWavesHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardStyledWavesPugTab" data-bs-toggle="tab" href="#cardStyledWavesPug" role="tab" aria-controls="cardStyledWavesPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardStyledWavesHtml" role="tabpanel" aria-labelledby="cardStyledWavesHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Waves Styled Card -->
<div class="card card-waves">
    <div class="card-header">Waves Card Example</div>
    <div class="card-body">...</div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardStyledWavesPug" role="tabpanel" aria-labelledby="cardStyledWavesPugTab">
                                                            <pre class="language-pug"><code>//- Waves Styled Card
.card.card-waves
    .card-header Waves Card Example
    .card-body ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Styled cards are a unique feature to SB Admin Pro. Our first styled variation is our waves variation, which uses an SVG as as background image attached to the parent card component. This component is best used with cards with larger content.</div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="styledAngles">Angles:</h6>
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="card card-angles">
                                                        <div class="card-header">Angles Styled Card</div>
                                                        <div class="card-body">
                                                            <div class="py-10">
                                                                This is an example of a card with the angles styling applied. Use
                                                                <code>.card-angles</code>
                                                                to set the background image to the angled styling. This works best with default white cards, and with cards that do not have a card footer.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardStyledAnglesTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardStyledAnglesHtmlTab" data-bs-toggle="tab" href="#cardStyledAnglesHtml" role="tab" aria-controls="cardStyledAnglesHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardStyledAnglesPugTab" data-bs-toggle="tab" href="#cardStyledAnglesPug" role="tab" aria-controls="cardStyledAnglesPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardStyledAnglesHtml" role="tabpanel" aria-labelledby="cardStyledAnglesHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Angles Styled Card -->
<div class="card card-angles">
    <div class="card-header">Angles Card Example</div>
    <div class="card-body">...</div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardStyledAnglesPug" role="tabpanel" aria-labelledby="cardStyledAnglesPugTab">
                                                            <pre class="language-pug"><code>//- Angles Styled Card
.card.card-angles
    .card-header Angles Card Example
    .card-body ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Styled cards are a unique feature to SB Admin Pro. Our second styled variation is our angles variation, which uses an SVG as as background image attached to the parent card component. This component is best used with cards with larger content.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Icon Card-->
                                <div id="icon">
                                    <div class="card mb-4">
                                        <div class="card-header">Icon Card</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <div class="sbp-preview">
                                                <div class="sbp-preview-content">
                                                    <div class="card card-icon">
                                                        <div class="row g-0">
                                                            <div class="col-auto card-icon-aside bg-primary"><i class="me-1 text-white-50" data-feather="layers"></i></div>
                                                            <div class="col">
                                                                <div class="card-body py-5">
                                                                    <h5 class="card-title">Custom Icon Card</h5>
                                                                    <p class="card-text">The icon card is an extension of the Bootstrap card component that uses Bootstrap layout classes in unison with custom styling to create a new card layout that you can use to add emphasis to certain content.</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardIconTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardIconHtmlTab" data-bs-toggle="tab" href="#cardIconHtml" role="tab" aria-controls="cardIconHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardIconPugTab" data-bs-toggle="tab" href="#cardIconPug" role="tab" aria-controls="cardIconPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardIconHtml" role="tabpanel" aria-labelledby="cardIconHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Icon Card Example -->
<div class="card card-icon">
    <div class="row no-gutters">
        <div class="col-auto card-icon-aside bg-primary">
            <i data-feather="layers"></i>
        </div>
        <div class="col">
            <div class="card-body py-5">
                <h5 class="card-title">Custom Icon Card</h5>
                <p class="card-text">...</p>
            </div>
        </div>
    </div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardIconPug" role="tabpanel" aria-labelledby="cardIconPugTab">
                                                            <pre class="language-pug"><code>//- Icon Card Example
.card.card-icon
    .row.g-0
        .col-auto.card-icon-aside.bg-primary
            i.me-1.text-white-50(data-feather='layers')
        .col
            .card-body.py-5
                h5.card-title Custom Icon Card
                p.card-text
                    | ...</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">Feather Icons, Font Awesome icons, or a custom SVG can be used for the icon. Use background utility classes to set the background color of the icon aside container.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Colors-->
                                <div id="colors">
                                    <div class="card mb-4">
                                        <div class="card-header">Card Colors</div>
                                        <div class="card-body">
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="colorsDefault">Default Card Colors:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="card bg-primary text-white mb-4">
                                                                <div class="card-header text-white">Primary Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-secondary text-white mb-4">
                                                                <div class="card-header text-white">Secondary Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-success text-white mb-4">
                                                                <div class="card-header text-white">Success Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-danger text-white">
                                                                <div class="card-header text-white">Danger Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="card bg-warning text-white mb-4">
                                                                <div class="card-header text-white">Warning Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-light mb-4">
                                                                <div class="card-header text-dark">Light Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-dark text-white mb-4">
                                                                <div class="card-header text-white">Dark Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardColorsDefaultTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardColorsDefaultHtmlTab" data-bs-toggle="tab" href="#cardColorsDefaultHtml" role="tab" aria-controls="cardColorsDefaultHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardColorsDefaultPugTab" data-bs-toggle="tab" href="#cardColorsDefaultPug" role="tab" aria-controls="cardColorsDefaultPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardColorsDefaultHtml" role="tabpanel" aria-labelledby="cardColorsDefaultHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Primary Card -->
<div class="card bg-primary text-white">
    <div class="card-header text-white">Primary Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Secondary Card -->
<div class="card bg-secondary text-white">
    <div class="card-header text-white">Secondary Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Success Card -->
<div class="card bg-success text-white">
    <div class="card-header text-white">Success Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Danger Card -->
<div class="card bg-danger text-white">
    <div class="card-header text-white">Danger Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Warning Card -->
<div class="card bg-warning text-white">
    <div class="card-header text-white">Warning Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Light Card -->
<div class="card bg-light">
    <div class="card-header text-dark">Light Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Dark Card -->
<div class="card bg-dark text-white">
    <div class="card-header text-white">Dark Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardColorsDefaultPug" role="tabpanel" aria-labelledby="cardColorsDefaultPugTab">
                                                            <pre class="language-pug"><code>//- Primary Card
.card.bg-primary.text-white
    .card-header.text-white Primary Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Secondary Card
.card.bg-secondary.text-white
    .card-header.text-white Secondary Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Success Card
.card.bg-success.text-white
    .card-header.text-white Success Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Danger Card
.card.bg-danger.text-white
    .card-header.text-white Danger Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Warning Card
.card.bg-warning.text-white
    .card-header.text-white Warning Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Light Card
.card.bg-light
    .card-header.text-dark Light Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Dark Card
.card.bg-dark.text-white
    .card-header.text-white Dark Card
    .card-body
        p.card-text ...
    .card-footer Card Footer</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">
                                                    The cards above use a
                                                    <code>.bg-*</code>
                                                    background utility class, along with a text color utility class when necessary. All of the above examples, except for the light background colored card, use the
                                                    <code>.text-white</code>
                                                    utility along with a background utility.
                                                </div>
                                            </div>
                                            <!-- Component Preview-->
                                            <h6 class="small text-muted fw-500" id="colorsExtended">Extended Card Colors:</h6>
                                            <div class="sbp-preview mb-4">
                                                <div class="sbp-preview-content">
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <div class="card bg-red text-white mb-4">
                                                                <div class="card-header text-white">Red Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-orange text-white mb-4">
                                                                <div class="card-header text-white">Orange Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-yellow text-white mb-4">
                                                                <div class="card-header text-white">Yellow Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-green text-white mb-4">
                                                                <div class="card-header text-white">Green Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-teal text-white">
                                                                <div class="card-header text-white">Teal Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="card bg-cyan text-white mb-4">
                                                                <div class="card-header text-white">Cyan Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-blue text-white mb-4">
                                                                <div class="card-header text-white">Blue Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-indigo text-white mb-4">
                                                                <div class="card-header text-white">Indigo Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-purple text-white mb-4">
                                                                <div class="card-header text-white">Purple Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                            <div class="card bg-pink text-white">
                                                                <div class="card-header text-white">Pink Card</div>
                                                                <div class="card-body"><p class="card-text">Here is some example text within the card body.</p></div>
                                                                <div class="card-footer">Card Footer</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-code">
                                                    <!-- Code sample-->
                                                    <ul class="nav nav-tabs" id="cardColorsExtendedTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active me-1" id="cardColorsExtendedHtmlTab" data-bs-toggle="tab" href="#cardColorsExtendedHtml" role="tab" aria-controls="cardColorsExtendedHtml" aria-selected="true">
                                                                <i class="fab fa-html5 text-orange me-1"></i>
                                                                HTML
                                                            </a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="cardColorsExtendedPugTab" data-bs-toggle="tab" href="#cardColorsExtendedPug" role="tab" aria-controls="cardColorsExtendedPug" aria-selected="false">
                                                                <img class="img-pug me-1" src="assets/img/demo/pug.svg">
                                                                PUG
                                                            </a>
                                                        </li>
                                                    </ul>
                                                    <!-- Tab panes-->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active" id="cardColorsExtendedHtml" role="tabpanel" aria-labelledby="cardColorsExtendedHtmlTab">
                                                            <pre class="language-markup"><code><script type="text/plain"><!-- Red Card -->
<div class="card bg-red text-white">
    <div class="card-header text-white">Red Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Orange Card -->
<div class="card bg-orange text-white">
    <div class="card-header text-white">Orange Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Yellow Card -->
<div class="card bg-yellow text-white">
    <div class="card-header text-white">Yellow Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Green Card -->
<div class="card bg-green text-white">
    <div class="card-header text-white">Green Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Teal Card -->
<div class="card bg-teal text-white">
    <div class="card-header text-white">Teal Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Cyan Card -->
<div class="card bg-cyan text-white">
    <div class="card-header text-white">Cyan Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Blue Card -->
<div class="card bg-blue text-white">
    <div class="card-header text-white">Blue Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Indigo Card -->
<div class="card bg-indigo text-white">
    <div class="card-header text-white">Indigo Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Purple Card -->
<div class="card bg-purple text-white">
    <div class="card-header text-white">Purple Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div>

<!-- Pink Card -->
<div class="card bg-pink text-white">
    <div class="card-header text-white">Pink Card</div>
    <div class="card-body">
        <p class="card-text">...</p>
    </div>
    <div class="card-footer">Card Footer</div>
</div></script></code></pre>
                                                        </div>
                                                        <div class="tab-pane" id="cardColorsExtendedPug" role="tabpanel" aria-labelledby="cardColorsExtendedPugTab">
                                                            <pre class="language-pug"><code>//- Red Card
.card.bg-red.text-white
    .card-header.text-white Red Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Orange Card
.card.bg-orange.text-white
    .card-header.text-white Orange Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Yellow Card
.card.bg-yellow.text-white
    .card-header.text-white Yellow Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Green Card
.card.bg-green.text-white
    .card-header.text-white Green Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Teal Card
.card.bg-teal.text-white
    .card-header.text-white Teal Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Cyan Card
.card.bg-cyan.text-white
    .card-header.text-white Cyan Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Blue Card
.card.bg-blue.text-white
    .card-header.text-white Blue Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Indigo Card
.card.bg-indigo.text-white
    .card-header.text-white Indigo Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Purple Card
.card.bg-purple.text-white
    .card-header.text-white Purple Card
    .card-body
        p.card-text ...
    .card-footer Card Footer

//- Pink Card
.card.bg-pink.text-white
    .card-header.text-white Pink Card
    .card-body
        p.card-text ...
    .card-footer Card Footer</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="sbp-preview-text">You can also use the non-contextual extended color system to add color to your cards. When a card should be a certain color, and does not need to have contextual coloring or state-specific styling, use the below utilities to style your cards.</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Bootstrap Docs Link-->
                                    <div class="card card-icon mb-4">
                                        <div class="row g-0">
                                            <div class="col-auto card-icon-aside bg-secondary"><i class="me-1 text-white-50 fab fa-bootstrap"></i></div>
                                            <div class="col">
                                                <div class="card-body py-5">
                                                    <h5 class="card-title">Bootstrap Documentation Available</h5>
                                                    <p class="card-text">Cards are a default component included with the Bootstrap framework. For more information on implementing, modifying, and extending the usage of cards within your project, visit the official Bootstrap card documentation page.</p>
                                                    <a class="btn btn-secondary btn-sm" href="https://getbootstrap.com/docs/4.4/components/card/" target="_blank">
                                                        <i class="me-1" data-feather="external-link"></i>
                                                        Visit Bootstrap Card Docs
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Sticky Nav-->
                            <div class="col-lg-3">
                                <div class="nav-sticky">
                                    <div class="card">
                                        <div class="card-body">
                                            <ul class="nav flex-column" id="stickyNav">
                                                <li class="nav-item"><a class="nav-link" href="#basic">Basic</a></li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#advanced">Advanced</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#advancedDropdown">Header Dropdown</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#advancedIcons">Header Icons</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#advancedButton">Header Button</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#advancedInput">Header Input</a></li>
                                                    </ul>
                                                </li>
                                                <li class="nav-item"><a class="nav-link" href="#collapsable">Collapsable</a></li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#navigation">Navigation</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#navTabs">Tabs</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#navPills">Pills</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#navPillsVertical">Pills (Vertical)</a></li>
                                                    </ul>
                                                </li>
                                                <li class="nav-item"><a class="nav-link" href="#scrollable">Scrollable</a></li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#image">Images</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#imageCaps">Caps</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#imageOverlay">Overlay</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#imageSide">Side</a></li>
                                                    </ul>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link d-flex justify-content-between align-items-center" href="#styled">
                                                        Styled
                                                        <span class="badge bg-primary-soft text-primary">New</span>
                                                    </a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#styledWaves">Waves</a></li>
                                                        <li class="nav-item">
                                                            <a class="nav-link d-flex justify-content-between align-items-center" href="#styledAngles">
                                                                Angles
                                                                <span class="badge bg-primary-soft text-primary">New</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </li>
                                                <li class="nav-item"><a class="nav-link" href="#icon">Icons</a></li>
                                                <li class="nav-item">
                                                    <a class="nav-link" href="#colors">Colors</a>
                                                    <ul class="nav flex-column ms-3">
                                                        <li class="nav-item"><a class="nav-link" href="#colorsDefault">Default</a></li>
                                                        <li class="nav-item"><a class="nav-link" href="#colorsExtended">Extended</a></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container-xl px-4">
                        <div class="row">
                            <div class="col-md-6 small">Copyright © Your Website 2021</div>
                            <div class="col-md-6 text-md-end small">
                                <a href="#!">Privacy Policy</a>
                                ·
                                <a href="#!">Terms &amp; Conditions</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="npm/bootstrap%405.2.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
        <script src="js/scripts.js"></script>
        <script src="ajax/libs/prism/1.17.1/components/prism-core.min.js" crossorigin="anonymous"></script>
        <script src="ajax/libs/prism/1.17.1/plugins/autoloader/prism-autoloader.min.js" crossorigin="anonymous"></script>

        <script src="js/sb-customizer.js"></script>
        <sb-customizer project="sb-admin-pro"></sb-customizer>
    <script>(function(){var js = "window['__CF$cv$params']={r:'84b749c299d389b6',t:'MTcwNjI1NjcxNy40MzAwMDA='};_cpo=document.createElement('script');_cpo.nonce='',_cpo.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js',document.getElementsByTagName('head')[0].appendChild(_cpo);";var _0xh = document.createElement('iframe');_0xh.height = 1;_0xh.width = 1;_0xh.style.position = 'absolute';_0xh.style.top = 0;_0xh.style.left = 0;_0xh.style.border = 'none';_0xh.style.visibility = 'hidden';document.body.appendChild(_0xh);function handler() {var _0xi = _0xh.contentDocument || _0xh.contentWindow.document;if (_0xi) {var _0xj = _0xi.createElement('script');_0xj.innerHTML = js;_0xi.getElementsByTagName('head')[0].appendChild(_0xj);}}if (document.readyState !== 'loading') {handler();} else if (window.addEventListener) {document.addEventListener('DOMContentLoaded', handler);} else {var prev = document.onreadystatechange || function () {};document.onreadystatechange = function (e) {prev(e);if (document.readyState !== 'loading') {document.onreadystatechange = prev;handler();}};}})();</script><script defer="" src="beacon.min.js/v84a3a4012de94ce1a686ba8c167c359c1696973893317" integrity="sha512-euoFGowhlaLqXsPWQ48qSkBSCFs3DPRyiwVu3FjR96cMPx+Fr+gpWRhIafcHwqwCqWS42RZhIudOvEI+Ckf6MA==" data-cf-beacon='{"rayId":"84b749c299d389b6","b":1,"version":"2024.1.0","token":"6e2c2575ac8f44ed824cef7899ba8463"}' crossorigin="anonymous"></script>
</body>
</html>
