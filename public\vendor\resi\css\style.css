/* Prevent font size inflation */
html {
    -moz-text-size-adjust: none;
    -webkit-text-size-adjust: none;
    text-size-adjust: none;
}

html,
body {
    margin: 6px;
    padding: 0px;
    font-size: 16px;
    line-height: 20px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--primary-color);
}

p {
    margin: 2px;
}

.container {
    width: 800px;
}

.main-box {
    border: 1px black solid;
}

.wrapper {
    display: flex;
    border-bottom: 1px black solid;
}

.left-side {
    width: 60%;
}

.right-side {
    width: 40%;
}

.centering-item {
    display: flex;
    justify-content: center;
    align-items: center;
}

.right-border {
    border-right: 1px black solid;
}

.bottom-border {
    border-bottom: 1px black solid;
}

.button-red {
    border-radius: 100px;
    background-color: red;
    color: white;
    text-align: center;
    padding: 8px;
    width: 120px;
    cursor: pointer;
    border: none;
}

.button-red:hover {
    opacity: 70%;
}

.barcode-container {
    width: 200px; /* Adjust the width as needed */
    height: auto; /* Maintain aspect ratio */
}
